package br.com.alice.staff.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import br.com.alice.data.layer.models.ModalityType
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.models.StructuredAddressReferenceModel
import br.com.alice.data.layer.models.withAddress
import br.com.alice.data.layer.models.withContacts
import br.com.alice.data.layer.models.withStaff
import br.com.alice.data.layer.services.HealthProfessionalModelDataService
import br.com.alice.data.layer.services.StaffModelDataService
import br.com.alice.staff.client.ContactService
import br.com.alice.staff.client.StaffFilters
import br.com.alice.staff.converters.toModel
import br.com.alice.staff.event.HealthProfessionalChangedEvent
import br.com.alice.staff.event.HealthProfessionalCreatedEvent
import br.com.alice.staff.event.HealthProfessionalUpdatedEvent
import br.com.alice.staff.event.StaffCreatedEvent
import br.com.alice.staff.event.StaffUpdatedEvent
import br.com.alice.staff.models.StaffHpKey
import br.com.alice.staff.models.StaffValidation
import br.com.alice.staff.models.StaffValidationErrors
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test


class StaffServiceImplTest {
    private val staffDataService: StaffModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val healthProfessionalDataService: HealthProfessionalModelDataService = mockk()
    private val contactService: ContactService = mockk()
    private val staffService = StaffServiceImpl(
        staffDataService,
        kafkaProducerService,
        healthProfessionalDataService,
        contactService
    )

    private val staff = TestModelFactory.buildStaff(
        role = Role.PRODUCT_TECH
    )
    private val staffModel = staff.toModel()

    private val otherStaff = TestModelFactory.buildStaff()
    private val otherStaffModel = otherStaff.toModel()

    private val healthProfessionalStaff = TestModelFactory.buildStaff(
        type = StaffType.HEALTH_PROFESSIONAL,
        role = Role.HEALTHCARE_TEAM_NURSE
    )
    private val healthProfessionalStaffModel = healthProfessionalStaff.toModel()

    private val updatedHealthProfessionalStaff = healthProfessionalStaff.copy(
        version = healthProfessionalStaff.version + 1,
        updatedAt = LocalDateTime.now()
    )
    private val healthProfessional = TestModelFactory.buildHealthProfessional(
        email = healthProfessionalStaff.email,
        name = healthProfessionalStaff.firstName,
        nationalId = healthProfessionalStaff.nationalId,
        type = healthProfessionalStaff.type,
        gender = healthProfessionalStaff.gender,
        role = healthProfessionalStaff.role,
        staffId = healthProfessionalStaff.id,
        imageUrl = healthProfessionalStaff.profileImageUrl,
    ).copy(
        status = SpecialistStatus.ACTIVE,
        healthSpecialistScore = HealthSpecialistScoreEnum.IS_RAISING_THE_BAR,
        theoristTier = SpecialistTier.EXPERT,
    )

    @BeforeTest
    fun setup() {
        clearMocks(
            staffDataService,
            kafkaProducerService,
            healthProfessionalDataService,
        )
    }

    @Test
    fun `#findWithAnyRole should find with any role`() = runBlocking {
        val roles = listOf(Role.HEALTHCARE_TEAM_NURSE, Role.MANAGER_PHYSICIAN)

        coEvery {
            staffDataService.find(
                queryEq {
                    where {
                        this.role.inList(roles)
                    }
                }
            )
        } returns listOf(staffModel, otherStaffModel).success()

        val returnedResult = staffService.findWithAnyRole(roles)

        assertThat(returnedResult).isSuccessWithData(listOf(staff, otherStaff))
    }

    @Test
    fun `#findActivesWithAnyRole should find with any role`() = runBlocking {
        val roles = listOf(Role.HEALTHCARE_TEAM_NURSE, Role.MANAGER_PHYSICIAN)

        coEvery {
            staffDataService.find(
                queryEq {
                    where {
                        this.role.inList(roles) and this.active.eq(true)
                    }
                }
            )
        } returns listOf(staffModel, otherStaffModel).success()

        val returnedResult = staffService.findActivesWithAnyRole(roles)

        assertThat(returnedResult).isSuccessWithData(listOf(staff, otherStaff))
    }

    @Test
    fun `#findByEmail should return Staff when find it`() = runBlocking {
        val staff = TestModelFactory.buildStaff()

        coEvery {
            staffDataService.findOne(
                queryEq { where { email.eq(staff.email) } }
            )
        } returns staff.toModel().success()

        val result = staffService.findByEmail(staff.email)

        assertThat(result).isSuccessWithData(staff)
    }

    @Test
    fun `#findByEmailList should return Staff list when find it`() = runBlocking {
        val staff = TestModelFactory.buildStaff()
        val staffList = listOf(staff)
        val emailList = listOf(staff.email)

        coEvery {
            staffDataService.find(
                queryEq {
                    where {
                        email.inList(emailList) and
                                active.eq(true)
                    }
                }
            )
        } returns listOf(staff.toModel()).success()

        val result = staffService.findByEmailList(emailList)

        assertThat(result).isSuccessWithData(staffList)

        coVerifyOnce { staffDataService.find(any()) }
    }

    @Test
    fun `#findByTokenAndRange should return Staff list`() = runBlocking {
        val range = IntRange(0, 10)
        val expectedResult = listOf(staff)
        val term = "STAFF"

        coEvery {
            staffDataService.find(queryEq {
                where { searchTokens.search(term) and this.active.eq(true) }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.count() }
            })
        } returns listOf(staffModel).success()

        val result = staffService.findByTokenAndRange(term, range)
        assertThat(result).isSuccessWithData(expectedResult)

        coVerifyOnce { staffDataService.find(any()) }
    }

    @Test
    fun `#findByNameWithRoleAndRange should return Staff list`() = runBlocking {
        val staff = TestModelFactory.buildStaff()
        val roles = listOf(staff.role)
        val intRange = IntRange(0, 49)

        coEvery {
            staffDataService.find(
                queryEq {
                    where {
                        role.inList(roles) and
                                searchTokens.search(staff.firstName)
                    }.orderBy { email }
                        .sortOrder { asc }
                        .offset { intRange.first }
                        .limit { intRange.count() }
                }
            )
        } returns listOf(staff.toModel()).success()

        val result = staffService.findByNameWithRoleAndRange(staff.firstName, roles, intRange)

        assertThat(result).isSuccessWithData(listOf(staff))

        coVerifyOnce { staffDataService.find(any()) }
    }

    @Test
    fun `#findByRoleAndRange should return Staff when find it`() = runBlocking {
        val staff = TestModelFactory.buildStaff()

        coEvery {
            staffDataService.find(
                queryEq {
                    where {
                        role.eq(staff.role)
                    }
                        .offset { 0 }
                        .limit { 2 }
                }
            )
        } returns listOf(staff.toModel()).success()

        val result = staffService.findByRoleAndRange(staff.role, IntRange(0, 1))

        assertThat(result).isSuccessWithData(listOf(staff))
    }

    @Test
    fun `#findByEmail should return NotFoundException when not find it`() = runBlocking {
        val staff = TestModelFactory.buildStaff()

        coEvery {
            staffDataService.findOne(
                queryEq { where { email.eq(staff.email) } }
            )
        } returns NotFoundException().failure()

        val result = staffService.findByEmail(staff.email)

        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#findByList should find by list`() = runBlocking {
        val staffIds = listOf(staff.id, RangeUUID.generate())

        coEvery {
            staffDataService.find(
                queryEq {
                    where {
                        id.inList(staffIds)
                    }
                }
            )
        } returns listOf(staffModel).success()

        val returnedResult = staffService.findByList(staffIds)

        assertThat(returnedResult).isSuccessWithData(listOf(staff))
    }

    @Test
    fun `#findByList returns empty list when ids is empty`() = runBlocking {
        val staffIds = emptyList<UUID>()

        val result = staffService.findByList(staffIds)
        assertThat(result).isSuccessWithData(emptyList())

        coVerify { staffDataService wasNot called }
        coVerify { kafkaProducerService wasNot called }
        coVerify { healthProfessionalDataService wasNot called }
    }

    @Test
    fun `#searchActiveStaff should search staff by name prefix`() = runBlocking {
        val namePrefix = staff.firstName.substring(0, 4)

        coEvery {
            staffDataService.find(
                queryEq {
                    where {
                        searchTokens.search(namePrefix) and
                                this.active.eq(true)
                    }
                }
            )
        } returns listOf(staffModel).success()

        val result = staffService.searchActiveStaff(namePrefix)

        assertThat(result).isSuccessWithData(listOf(staff))
    }

    @Test
    fun `#add should also add new health professional with contact`() = runBlocking {
        val contact = TestModelFactory.buildContact(
            modality = ModalityType.REMOTE,
            addressId = null,
        )
        val contacts = listOf(contact)
        val healthProfessional = healthProfessional.copy(
            scheduleAvailabilityDays = contacts.mapNotNull { it.scheduleAvailabilityDays }.firstNotNullOfOrNull { it },
            appointmentTypes = listOf(SpecialistAppointmentType.REMOTE),
            contactIds = contacts.map { it.id },
            phones = contacts.map { it.phones }.flatten().distinctBy { it.phone },
        )
        coEvery { contactService.upsertWithAddress(contact, null) } returns contact.success()
        coEvery {
            staffDataService.add(healthProfessionalStaffModel)
        } returns healthProfessionalStaffModel.success()

        coEvery {
            kafkaProducerService.produce(
                StaffCreatedEvent(healthProfessionalStaff)
            )
        } returns mockk()

        coEvery {
            healthProfessionalDataService.add(healthProfessional.toModel())
        } returns healthProfessional.toModel().success()

        coEvery {
            kafkaProducerService.produce(
                HealthProfessionalCreatedEvent(
                    healthProfessional.withStaff(
                        healthProfessionalStaff
                    ).withContacts(listOf(contact))
                )
            )
        } returns mockk()

        coEvery {
            kafkaProducerService.produce(
                match {
                    if (it is HealthProfessionalChangedEvent) {
                        it.healthProfessional == healthProfessional.withStaff(
                            healthProfessionalStaff
                        ).withContacts(listOf(contact)) && it.eventAction == NotificationEventAction.CREATED
                    } else false
                }
            )
        } returns mockk()

        val result = staffService.add(healthProfessionalStaff, healthProfessional, listOf(contact))

        assertThat(result).isSuccessWithData(healthProfessionalStaff)

        coVerifyOnce { staffDataService.add(any()) }
        coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        coVerifyOnce { healthProfessionalDataService.add(any()) }
        coVerifyOnce { contactService.upsertWithAddress(any(), any()) }
    }

    @Test
    fun `#add should return error when active COMMUNITY_SPECIALIST is invalid`() = runBlocking {
        val healthProfessionalStaff =
            healthProfessionalStaff.copy(type = StaffType.COMMUNITY_SPECIALIST, role = Role.COMMUNITY)
        val healthProfessional = healthProfessional.copy(
            type = StaffType.COMMUNITY_SPECIALIST, healthSpecialistScore = null
        )

        val result = staffService.add(healthProfessionalStaff, healthProfessional)

        ResultAssert(result)
            .fails()
            .withMessage("Especialista da Comunidade deve ter o campo Vivendo o Impossível definido")
            .ofType(InvalidArgumentException::class)

        coVerifyNone { staffDataService.add(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }
        coVerifyNone { healthProfessionalDataService.add(any()) }
        coVerifyNone { contactService.upsertWithAddress(any(), any()) }
    }

    @Test
    fun `#add should return error when type can not have a given role`() = runBlocking {
        val healthProfessionalStaff =
            healthProfessionalStaff.copy(type = StaffType.COMMUNITY_SPECIALIST, role = Role.PRODUCT_TECH)
        val healthProfessional =
            healthProfessional.copy(type = StaffType.COMMUNITY_SPECIALIST, healthSpecialistScore = null)

        val result = staffService.add(healthProfessionalStaff, healthProfessional)

        ResultAssert(result)
            .fails()
            .withMessage("<COMMUNITY_SPECIALIST> can not be a <PRODUCT_TECH> role")
            .ofType(IllegalArgumentException::class)

        coVerifyNone { staffDataService.add(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }
        coVerifyNone { healthProfessionalDataService.add(any()) }
        coVerifyNone { contactService.upsertWithAddress(any(), any()) }
    }

    @Test
    fun `#add should add when active COMMUNITY_SPECIALIST when is valid`() = runBlocking {
        val healthProfessionalStaff =
            healthProfessionalStaff.copy(type = StaffType.COMMUNITY_SPECIALIST, role = Role.COMMUNITY)

        val contact = TestModelFactory.buildContact(
            modality = ModalityType.REMOTE,
            addressId = null,
        )
        val contacts = listOf(contact)
        val healthProfessional = healthProfessional.copy(
            scheduleAvailabilityDays = contacts.mapNotNull { it.scheduleAvailabilityDays }.firstNotNullOfOrNull { it },
            appointmentTypes = listOf(SpecialistAppointmentType.REMOTE),
            contactIds = contacts.map { it.id },
            phones = contacts.map { it.phones }.flatten().distinctBy { it.phone },
        )
        coEvery { contactService.upsertWithAddress(contact, null) } returns contact.success()
        coEvery {
            staffDataService.add(healthProfessionalStaff.toModel())
        } returns healthProfessionalStaff.toModel().success()

        coEvery {
            kafkaProducerService.produce(
                StaffCreatedEvent(healthProfessionalStaff)
            )
        } returns mockk()

        coEvery {
            healthProfessionalDataService.add(healthProfessional.toModel())
        } returns healthProfessional.toModel().success()

        coEvery {
            kafkaProducerService.produce(
                HealthProfessionalCreatedEvent(
                    healthProfessional.withStaff(
                        healthProfessionalStaff
                    ).withContacts(listOf(contact))
                )
            )
        } returns mockk()

        coEvery {
            kafkaProducerService.produce(
                match {
                    if (it is HealthProfessionalChangedEvent) {
                        it.healthProfessional == healthProfessional.withStaff(
                            healthProfessionalStaff
                        ).withContacts(listOf(contact)) && it.eventAction == NotificationEventAction.CREATED
                    } else false
                }
            )
        } returns mockk()

        val result = staffService.add(healthProfessionalStaff, healthProfessional, listOf(contact))

        assertThat(result).isSuccessWithData(healthProfessionalStaff)

        coVerifyOnce { staffDataService.add(any()) }
        coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        coVerifyOnce { healthProfessionalDataService.add(any()) }
        coVerifyOnce { contactService.upsertWithAddress(any(), any()) }
    }

    @Test
    fun `#findByRange returns a list of staff`() = runBlocking {
        val range = IntRange(0, 20)

        coEvery {
            staffDataService.find(
                queryEq {
                    orderBy { email }
                        .sortOrder { asc }
                        .offset { 0 }
                        .limit { 21 }
                }
            )
        } returns listOf(staffModel).success()

        val result = staffService.findByRange(range)

        assertThat(result).isSuccessWithData(listOf(staff))

        coVerifyOnce { staffDataService.find(any()) }
    }

    @Test
    fun `#findByRange returns an error`() = runBlocking {
        val range = IntRange(0, 20)

        coEvery {
            staffDataService.find(
                queryEq {
                    orderBy { email }
                        .sortOrder { asc }
                        .offset { 0 }
                        .limit { 21 }
                }
            )
        } returns Exception().failure()

        val result = staffService.findByRange(range)

        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { staffDataService.find(any()) }
    }

    @Test
    fun `#get returns a staff found by id`() = runBlocking {
        coEvery {
            staffDataService.get(staff.id)
        } returns staffModel.success()

        val result = staffService.get(staff.id)

        assertThat(result).isSuccessWithData(staff)

        coVerifyOnce { staffDataService.get(any()) }
    }

    @Test
    fun `#get returns an error`() = runBlocking {
        coEvery {
            staffDataService.get(staff.id)
        } returns Exception().failure()

        val result = staffService.get(staff.id)

        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { staffDataService.get(any()) }
    }

    @Test
    fun `#getActive returns active staff found by id`() = runBlocking {
        coEvery {
            staffDataService.findOne(queryEq {
                where {
                    this.id.eq(staff.id) and this.active.eq(true)
                }
            })
        } returns staffModel.success()

        val result = staffService.getActive(staff.id)

        assertThat(result).isSuccessWithData(staff)
    }

    @Test
    fun `#update should also update health professional with contact`() = runBlocking {
        val address = TestModelFactory.buildStructuredAddress(
            referencedModelId = healthProfessional.id,
            referencedModelClass = StructuredAddressReferenceModel.HEALTH_PROFESSIONAL
        )
        val contactToAdd = TestModelFactory.buildContact(
            modality = ModalityType.PRESENTIAL,
            addressId = address.id,
        ).withAddress(address)

        val inactiveContact = TestModelFactory.buildContact(
            modality = ModalityType.REMOTE,
            addressId = null,
        )
        val contacts = listOf(contactToAdd)
        val healthProfessional = healthProfessional.copy(contactIds = listOf(inactiveContact.id))
        val newHealthProfessional = healthProfessional.copy(
            scheduleAvailabilityDays = contacts.mapNotNull { it.scheduleAvailabilityDays }.firstNotNullOfOrNull { it },
            appointmentTypes = listOf(SpecialistAppointmentType.PRESENTIAL),
            contactIds = contacts.map { it.id },
            phones = contacts.map { it.phones }.flatten().distinctBy { it.phone },
        )

        coEvery { staffDataService.update(healthProfessionalStaffModel) } returns updatedHealthProfessionalStaff.toModel()
            .success()
        coEvery {
            contactService.get(
                inactiveContact.id,
            )
        } returns inactiveContact.success()
        coEvery {
            healthProfessionalDataService.findOne(
                queryEq { where { this.staffId.eq(healthProfessionalStaff.id) } }
            )
        } returns healthProfessional.toModel().success()
        coEvery {
            contactService.upsertWithAddress(
                contactToAdd,
                contactToAdd.address
            )
        } returns contactToAdd.success()
        coEvery { contactService.inactiveWithAddress(inactiveContact) } returns true.success()
        coEvery { healthProfessionalDataService.update(any()) } returns newHealthProfessional.toModel().success()
        coEvery {
            kafkaProducerService.produce(
                StaffUpdatedEvent(
                    healthProfessionalStaff.id,
                    updatedStaff = updatedHealthProfessionalStaff,
                    oldStaff = healthProfessionalStaff
                )
            )
        } returns mockk()
        coEvery { kafkaProducerService.produce(HealthProfessionalUpdatedEvent(healthProfessional.id)) } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                HealthProfessionalChangedEvent(healthProfessional, NotificationEventAction.UPDATED)
            )
        } returns mockk()
        coEvery { staffDataService.get(healthProfessionalStaff.id) } returns healthProfessionalStaffModel.success()

        val result =
            staffService.update(healthProfessionalStaff, healthProfessional, contacts = listOf(contactToAdd))
        assertThat(result).isSuccessWithData(updatedHealthProfessionalStaff)

        coVerifyOnce { staffDataService.update(any()) }
        coVerifyOnce { healthProfessionalDataService.findOne(any()) }
        coVerifyOnce { healthProfessionalDataService.update(any()) }
        coVerifyOnce { contactService.get(any()) }
        coVerifyOnce { contactService.inactiveWithAddress(any()) }
        coVerifyOnce { contactService.upsertWithAddress(any(), any()) }
        coVerifyOnce { staffDataService.get(any()) }
        coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#update should also staff and create health professional with addresses`() = runBlocking {
        val address = TestModelFactory.buildStructuredAddress(
            referencedModelId = healthProfessional.id,
            referencedModelClass = StructuredAddressReferenceModel.HEALTH_PROFESSIONAL
        )
        val contactToAdd = TestModelFactory.buildContact(
            modality = ModalityType.PRESENTIAL,
            addressId = address.id,
        ).withAddress(address)

        val contacts = listOf(contactToAdd)
        val newHealthProfessional = healthProfessional.copy(
            scheduleAvailabilityDays = contacts.mapNotNull { it.scheduleAvailabilityDays }.firstNotNullOfOrNull { it },
            appointmentTypes = listOf(SpecialistAppointmentType.PRESENTIAL),
            contactIds = contacts.map { it.id },
            phones = contacts.map { it.phones }.flatten().distinctBy { it.phone },
        )

        coEvery { staffDataService.update(healthProfessionalStaffModel) } returns updatedHealthProfessionalStaff.toModel()
            .success()
        coEvery {
            healthProfessionalDataService.findOne(
                queryEq { where { this.staffId.eq(healthProfessionalStaff.id) } }
            )
        } returns NotFoundException().failure()
        coEvery {
            contactService.upsertWithAddress(
                contactToAdd,
                contactToAdd.address
            )
        } returns contactToAdd.success()
        coEvery { staffDataService.get(healthProfessionalStaff.id) } returns healthProfessionalStaffModel.success()
        coEvery { contactService.upsertWithAddress(contactToAdd, contactToAdd.address) } returns contactToAdd.success()
        coEvery { healthProfessionalDataService.add(newHealthProfessional.toModel()) } returns newHealthProfessional.toModel()
            .success()
        coEvery {
            kafkaProducerService.produce(
                StaffUpdatedEvent(
                    healthProfessionalStaff.id,
                    updatedStaff = updatedHealthProfessionalStaff,
                    oldStaff = healthProfessionalStaff
                )
            )
        } returns mockk()
        coEvery { kafkaProducerService.produce(HealthProfessionalCreatedEvent(newHealthProfessional)) } returns mockk()

        val result =
            staffService.update(healthProfessionalStaff, healthProfessional, contacts = listOf(contactToAdd))
        assertThat(result).isSuccessWithData(updatedHealthProfessionalStaff)

        coVerifyOnce { staffDataService.update(any()) }
        coVerifyOnce { contactService.upsertWithAddress(any(), any()) }
        coVerifyOnce { healthProfessionalDataService.add(any()) }
        coVerifyOnce { staffDataService.get(any()) }
    }

    @Test
    fun `#update should return error when active COMMUNITY_SPECIALIST is invalid`() = runBlocking {
        val healthProfessionalStaff =
            healthProfessionalStaff.copy(type = StaffType.COMMUNITY_SPECIALIST, role = Role.COMMUNITY)
        val healthProfessional = healthProfessional.copy(
            type = StaffType.COMMUNITY_SPECIALIST, healthSpecialistScore = null
        )

        val result = staffService.update(healthProfessionalStaff, healthProfessional)
        ResultAssert(result)
            .fails()
            .withMessage("Especialista da Comunidade deve ter o campo Vivendo o Impossível definido")
            .ofType(InvalidArgumentException::class)

        coVerifyNone { staffDataService.update(any()) }
        coVerifyNone { contactService.upsertWithAddress(any(), any()) }
        coVerifyNone { healthProfessionalDataService.add(any()) }
        coVerifyNone { staffDataService.get(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#update should return error when type can not have a given role`() = runBlocking {
        val healthProfessionalStaff =
            healthProfessionalStaff.copy(type = StaffType.COMMUNITY_SPECIALIST, role = Role.PRODUCT_TECH)
        val healthProfessional =
            healthProfessional.copy(type = StaffType.COMMUNITY_SPECIALIST, healthSpecialistScore = null)

        val result = staffService.update(healthProfessionalStaff, healthProfessional)
        ResultAssert(result)
            .fails()
            .withMessage("<COMMUNITY_SPECIALIST> can not be a <PRODUCT_TECH> role")
            .ofType(IllegalArgumentException::class)

        coVerifyNone { staffDataService.update(any()) }
        coVerifyNone { contactService.upsertWithAddress(any(), any()) }
        coVerifyNone { healthProfessionalDataService.add(any()) }
        coVerifyNone { staffDataService.get(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#update should update when active COMMUNITY_SPECIALIST is valid`() = runBlocking {
        val healthProfessionalStaff =
            healthProfessionalStaff.copy(type = StaffType.COMMUNITY_SPECIALIST, role = Role.COMMUNITY)

        val address = TestModelFactory.buildStructuredAddress(
            referencedModelId = healthProfessional.id,
            referencedModelClass = StructuredAddressReferenceModel.HEALTH_PROFESSIONAL
        )
        val contactToAdd = TestModelFactory.buildContact(
            modality = ModalityType.PRESENTIAL,
            addressId = address.id,
        ).withAddress(address)

        val inactiveContact = TestModelFactory.buildContact(
            modality = ModalityType.REMOTE,
            addressId = null,
        )
        val contacts = listOf(contactToAdd)
        val healthProfessional = healthProfessional.copy(
            contactIds = listOf(inactiveContact.id),
        )
        val newHealthProfessional = healthProfessional.copy(
            scheduleAvailabilityDays = contacts.mapNotNull { it.scheduleAvailabilityDays }.firstNotNullOfOrNull { it },
            appointmentTypes = listOf(SpecialistAppointmentType.PRESENTIAL),
            contactIds = contacts.map { it.id },
            phones = contacts.map { it.phones }.flatten().distinctBy { it.phone },
        )

        coEvery { staffDataService.update(healthProfessionalStaff.toModel()) } returns updatedHealthProfessionalStaff.toModel()
            .success()
        coEvery {
            contactService.get(
                inactiveContact.id,
            )
        } returns inactiveContact.success()
        coEvery {
            healthProfessionalDataService.findOne(
                queryEq { where { this.staffId.eq(healthProfessionalStaff.id) } }
            )
        } returns healthProfessional.toModel().success()
        coEvery {
            contactService.upsertWithAddress(
                contactToAdd,
                contactToAdd.address
            )
        } returns contactToAdd.success()
        coEvery { contactService.inactiveWithAddress(inactiveContact) } returns true.success()
        coEvery { healthProfessionalDataService.update(any()) } returns newHealthProfessional.toModel().success()
        coEvery {
            kafkaProducerService.produce(
                StaffUpdatedEvent(
                    healthProfessionalStaff.id,
                    updatedStaff = updatedHealthProfessionalStaff,
                    oldStaff = healthProfessionalStaff
                )
            )
        } returns mockk()
        coEvery { kafkaProducerService.produce(HealthProfessionalUpdatedEvent(healthProfessional.id)) } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                HealthProfessionalChangedEvent(healthProfessional, NotificationEventAction.UPDATED)
            )
        } returns mockk()
        coEvery { staffDataService.get(healthProfessionalStaff.id) } returns healthProfessionalStaff.toModel().success()

        val result =
            staffService.update(healthProfessionalStaff, healthProfessional, contacts = listOf(contactToAdd))
        assertThat(result).isSuccessWithData(updatedHealthProfessionalStaff)

        coVerifyOnce { staffDataService.update(any()) }
        coVerifyOnce { healthProfessionalDataService.findOne(any()) }
        coVerifyOnce { healthProfessionalDataService.update(any()) }
        coVerifyOnce { contactService.get(any()) }
        coVerifyOnce { contactService.inactiveWithAddress(any()) }
        coVerifyOnce { contactService.upsertWithAddress(any(), any()) }
        coVerifyOnce { staffDataService.get(any()) }
        coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
    }

    @OptIn(QueryAllUsage::class)
    @Test
    fun `#count returns a total staff count`() = runBlocking {
        coEvery {
            staffDataService.count(queryEq { all() })
        } returns 10.success()

        val result = staffService.count()

        assertThat(result).isSuccessWithData(10)

        coVerifyOnce { staffDataService.count(any()) }
    }

    @OptIn(QueryAllUsage::class)
    @Test
    fun `#count returns an error`() = runBlocking {
        coEvery {
            staffDataService.count(queryEq { all() })
        } returns Exception().failure()

        val result = staffService.count()

        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { staffDataService.count(any()) }
    }

    @Test
    fun `#searchActiveByNameAndRoleWithRange should find staff with all parameters`() = runBlocking {
        val ids = listOf(RangeUUID.generate())
        val namePrefix = staff.firstName.substring(0, 4)
        val roles = listOf(Role.HEALTHCARE_TEAM_NURSE)
        val types = listOf(StaffType.PITAYA)
        val range = IntRange(0, 2)

        coEvery {
            staffDataService.find(
                queryEq {
                    where {
                        this.id.inList(ids) and
                                searchTokens.search(namePrefix) and
                                this.role.inList(roles) and
                                this.active.eq(true) and
                                this.type.inList(types)
                    }.orderBy { email }
                        .sortOrder { asc }
                        .offset { range.first }
                        .limit { range.count() }
                }
            )
        } returns listOf(staffModel, otherStaffModel).success()

        val result = staffService.searchByNameAndRoleWithRange(
            ids, range, roles, namePrefix, true, types
        )

        assertThat(result).isSuccessWithData(listOf(staff, otherStaff))
    }

    @Test
    fun `#countActiveByNameAndRoleWithRange should count staff with all parameters`(): Unit = runBlocking {
        val ids = listOf(RangeUUID.generate())
        val roles = listOf(Role.HEALTHCARE_TEAM_NURSE)
        val namePrefix = staff.firstName.substring(0, 4)
        val types = listOf(StaffType.PITAYA)

        coEvery {
            staffDataService.count(
                queryEq {
                    where {
                        this.id.inList(ids) and
                                searchTokens.search(namePrefix) and
                                this.role.inList(roles) and
                                this.active.eq(true) and
                                this.type.inList(types)
                    }
                }
            )
        } returns 3.success()

        val result = staffService.countByNameAndRoleWithRange(
            ids, roles, namePrefix, true, types
        )

        assertThat(result).isSuccessWithData(3)
    }

    @Test
    fun `#searchActiveByNameAndRoleWithRange should find staff by role with range`() = runBlocking {
        val roles = listOf(Role.HEALTHCARE_TEAM_NURSE)
        val range = IntRange(0, 2)
        coEvery {
            staffDataService.find(queryEq {
                where { this.role.inList(roles) }
                    .orderBy { email }.sortOrder { asc }
                    .offset { range.first }.limit { range.count() }
            })
        } returns listOf(staffModel, otherStaffModel).success()

        val result = staffService.searchByNameAndRoleWithRange(null, range, roles, null, null)
        assertThat(result).isSuccessWithData(listOf(staff, otherStaff))
    }

    @Test
    fun `#searchActivesByIdsNameAndRole should return active staff by ID name and role`() = runBlocking {
        val staff = TestModelFactory.buildStaff()
        val staffList = listOf(staff)
        val staffIds = listOf(staff.id)

        coEvery {
            staffDataService.find(
                queryEq {
                    where {
                        id.inList(staffIds) and
                                searchTokens.search(staff.firstName) and
                                role.inList(listOf(staff.role)) and
                                active.eq(true)
                    }
                }
            )
        } returns listOf(staff.toModel()).success()

        val result = staffService.searchActivesByIdsNameAndRole(staffIds, staff.firstName, staff.role)
        assertThat(result).isSuccessWithData(staffList)

        coVerifyOnce { staffDataService.find(any()) }
    }

    @Test
    fun `#countActiveByNameAndRoleWithRange should count staff by name and role with range`(): Unit = runBlocking {
        val namePrefix = staff.firstName.substring(0, 4)
        val roles = listOf(Role.HEALTHCARE_TEAM_NURSE)

        coEvery {
            staffDataService.count(
                queryEq {
                    where {
                        searchTokens.search(namePrefix) and
                                this.role.inList(roles)
                    }
                }
            )
        } returns 3.success()

        val result = staffService.countByNameAndRoleWithRange(null, roles, namePrefix, null)

        assertThat(result).isSuccessWithData(3)
    }

    @Test
    fun `#findActivesById should return actives staffs by Id`() = runBlocking {
        val staffIds = listOf(staff.id)

        coEvery {
            staffDataService.find(
                queryEq {
                    where { id.inList(staffIds) and this.active.eq(true) }
                }
            )
        } returns listOf(staffModel).success()

        val result = staffService.findActivesById(staffIds)

        assertThat(result).isSuccessWithData(listOf(staff))
    }

    @Test
    fun `#countByRole should return count of staff by role`() = runBlocking {
        val roles = listOf(Role.HEALTHCARE_TEAM_NURSE)

        coEvery {
            staffDataService.count(
                queryEq {
                    where {
                        role.inList(roles)
                    }
                }
            )
        } returns 10.success()

        val result = staffService.countByRole(roles)

        assertThat(result).isSuccessWithData(10)

        coVerifyOnce { staffDataService.count(any()) }
    }

    @Test
    fun `#getActiveWithRole should return active staff by ID with role`() = runBlocking {
        val roles = listOf(Role.HEALTHCARE_TEAM_NURSE)

        coEvery {
            staffDataService.findOne(
                queryEq {
                    where {
                        this.id.eq(staff.id) and
                                this.active.eq(true) and
                                this.role.inList(roles)
                    }
                }
            )
        } returns staffModel.success()

        val returnedResult = staffService.getActiveWithRole(staff.id, roles)

        assertThat(returnedResult).isSuccessWithData(staff)
    }

    @Test
    fun `#findBy returns staff found by all parameters`() = runBlocking {
        val filters = StaffFilters(
            roles = listOf(Role.HEALTHCARE_TEAM_NURSE),
            emails = listOf("<EMAIL>"),
            searchTerm = "term",
            ids = listOf(RangeUUID.generate()),
            namePrefix = "prefix",
            active = true,
            types = listOf(StaffType.PITAYA),
            range = IntRange(0, 9)
        )

        coEvery {
            staffDataService.find(
                queryEq {
                    where {
                        this.role.inList(filters.roles!!) and
                                this.email.inList(filters.emails!!) and
                                this.searchTokens.search(filters.searchTerm!!) and
                                this.id.inList(filters.ids!!) and
                                this.firstName.like(filters.namePrefix!!) and
                                this.active.eq(filters.active!!) and
                                this.type.inList(filters.types!!)
                    }.offset { filters.range!!.first }
                        .limit { filters.range!!.count() }
                }
            )
        } returns listOf(staffModel).success()

        val result = staffService.findBy(filters)
        assertThat(result).isSuccessWithData(listOf(staff))

        coVerifyOnce { staffDataService.find(any()) }
    }

    @Test
    fun `#findBy returns empty list when filter is empty`() = runBlocking {
        val filters = StaffFilters()

        val result = staffService.findBy(filters)
        assertThat(result).isSuccessWithData(emptyList())

        coVerify { staffDataService wasNot called }
    }

    @Test
    fun `#countBy returns counted by all parameters`() = runBlocking {
        val filters = StaffFilters(
            roles = listOf(Role.HEALTHCARE_TEAM_NURSE),
            emails = listOf("<EMAIL>"),
            searchTerm = "term",
            ids = listOf(RangeUUID.generate()),
            namePrefix = "prefix",
            active = true,
            types = listOf(StaffType.PITAYA),
            range = IntRange(0, 9)
        )

        coEvery {
            staffDataService.count(
                queryEq {
                    where {
                        this.role.inList(filters.roles!!) and
                                this.email.inList(filters.emails!!) and
                                this.searchTokens.search(filters.searchTerm!!) and
                                this.id.inList(filters.ids!!) and
                                this.firstName.like(filters.namePrefix!!) and
                                this.active.eq(filters.active!!) and
                                this.type.inList(filters.types!!)
                    }
                }
            )
        } returns 1.success()

        val result = staffService.countBy(filters)
        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { staffDataService.count(any()) }
    }

    @OptIn(QueryAllUsage::class)
    @Test
    fun `#countBy returns counted by all when filter is empty`() = runBlocking {
        val filters = StaffFilters()

        coEvery { staffDataService.count(queryEq { all() }) } returns 1.success()

        val result = staffService.countBy(filters)
        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { staffDataService.count(any()) }
    }

    @Test
    fun `#saveProfileImageUrl save and return staff with url`() = runBlocking {
        val url = "http://url.com"
        val staff = TestModelFactory.buildStaff()
        val staffWithUrl = staff.copy(profileImageUrl = url)

        coEvery {
            staffDataService.get(staff.id)
        } returns staff.toModel().success()

        coEvery {
            staffDataService.update(
                staffWithUrl.toModel()
            )
        } returns staffWithUrl.toModel().success()

        val result = staffService.saveProfileImageUrl(staff.id, url)

        assertThat(result).isSuccessWithData(staffWithUrl)

        coVerifyOnce { staffDataService.update(any()) }
    }

    @Test
    fun `#setOnCall  - should update staff and update health_professional`() = runBlocking {
        val staffOncall = staff.copy(onCall = true)
        val healthProfessionalOncall = healthProfessional.copy(onCall = true)

        coEvery { staffDataService.get(staff.id) } returns staffModel.success()
        coEvery { staffDataService.update(staffOncall.toModel()) } returns staffOncall.toModel().success()

        coEvery {
            healthProfessionalDataService.findOne(
                queryEq {
                    where { this.staffId.eq(staff.id) }
                }
            )
        } returns healthProfessional.toModel().success()
        coEvery { healthProfessionalDataService.update(healthProfessionalOncall.toModel()) } returns healthProfessionalOncall.toModel()
            .success()

        val result = staffService.setOnCall(staff.id, true)
        assertThat(result).isSuccessWithData(staffOncall)

        coVerifyOnce { staffDataService.get(any()) }
        coVerifyOnce { staffDataService.update(any()) }
        coVerifyOnce { healthProfessionalDataService.findOne(any()) }
        coVerifyOnce { healthProfessionalDataService.update(any()) }

    }

    @Test
    fun `#setOnCall  - should update only staff if health_professional not exists`() = runBlocking {
        val staffOncall = staff.copy(onCall = true)

        coEvery { staffDataService.get(staff.id) } returns staffModel.success()
        coEvery { staffDataService.update(staffOncall.toModel()) } returns staffOncall.toModel().success()

        coEvery {
            healthProfessionalDataService.findOne(
                queryEq {
                    where { this.staffId.eq(staff.id) }
                }
            )
        } returns NotFoundException().failure()

        val result = staffService.setOnCall(staff.id, true)
        assertThat(result).isSuccessWithData(staffOncall)

        coVerifyOnce { staffDataService.get(any()) }
        coVerifyOnce { staffDataService.update(any()) }
        coVerifyOnce { healthProfessionalDataService.findOne(any()) }
        coVerifyNone { healthProfessionalDataService.update(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#validHealthProfessional - should return errors when health professional exists`() = runBlocking {
        val staff = TestModelFactory.buildStaff(
            type = StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
            role = Role.ANESTHETIST
        )
        val hp = TestModelFactory.buildHealthProfessional(
            staffId = staff.id,
            type = StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
            role = Role.ANESTHETIST,
            email = staff.email
        )
        val request = StaffHpKey(
            email = staff.email,
            council = hp.council
        )

        coEvery { staffDataService.findOne(queryEq { where { this.email.eq(request.email!!) } }) } returns staff.toModel()
            .success()
        coEvery {
            healthProfessionalDataService.findOne(queryEq {
                where {
                    this.email.eq(request.email!!) or this.council.eq(
                        hp.council.toModel()
                    )
                }
            })
        } returns hp.toModel().success()

        val result = staffService.validHealthProfessional(request)

        assertThat(result).isSuccessWithData(
            StaffValidation(
                errors = listOf(
                    StaffValidationErrors(
                        code = "email_exists",
                        message = "Staff already exists with email"
                    ),
                    StaffValidationErrors(
                        code = "email_exists",
                        message = "Health Professional already exists with email"
                    ),
                    StaffValidationErrors(
                        code = "council_exists",
                        message = "Health Professional already exists with council"
                    )
                )
            )
        )

    }

    @Test
    fun `#validHealthProfessional - should return errors when health professional email is invalid`() = runBlocking {
        val request = StaffHpKey(email = "invalid-email")

        coEvery { staffDataService.findOne(queryEq { where { this.email.eq(request.email!!) } }) } returns NotFoundException().failure()
        coEvery {
            healthProfessionalDataService.findOne(queryEq {
                where {
                    this.email.eq(request.email!!)
                }
            })
        } returns NotFoundException().failure()

        val result = staffService.validHealthProfessional(request)

        assertThat(result).isSuccessWithData(
            StaffValidation(
                errors = listOf(
                    StaffValidationErrors(
                        code = "invalid_email",
                        message = "Invalid email format"
                    )
                )
            )
        )
    }
}
