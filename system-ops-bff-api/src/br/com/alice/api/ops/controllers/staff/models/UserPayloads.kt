package br.com.alice.api.ops.controllers.staff.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.models.Gender
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.models.State
import br.com.alice.data.layer.models.AvailableDay
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import br.com.alice.data.layer.models.ModalityType
import br.com.alice.data.layer.models.OnCallPaymentMethod
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.Qualification
import java.time.LocalDate
import java.util.UUID

data class UserRequest(
    val firstName: String,
    val lastName: String,
    val email: String,
    val nationalId: String? = null,
    val birthdate: LocalDate? = null,
    val gender: Gender,
    val profileImageUrl: String? = null,
    val role: Role,
    val type: StaffType,
    val active: Boolean = true,
    val urlSlug: String? = null,
    val quote: String? = null,
    val profileBio: String? = null,
    val council: CouncilDTO? = null,
    val specialty: UUID? = null,
    val subSpecialties: List<UUID>? = emptyList(),
    val internalSpecialty: UUID? = null,
    val internalSubSpecialties: List<UUID>? = emptyList(),
    val providerUnits: List<UUID>? = emptyList(),
    val qualifications: List<Qualification>? = emptyList(),
    val tier: SpecialistTier? = null,
    val theoristTier: SpecialistTier? = null,
    val curiosity: String? = null,
    val showOnApp: Boolean? = null,
    val education: List<String>? = emptyList(),
    val healthSpecialistScore: HealthSpecialistScoreEnum? = null,
    val deAccreditationDate: LocalDate? = null,
    val contacts: List<ContactModel>? = emptyList(),
    val paymentFrequency: Int? = 0,
    val attendsToOnCall: Boolean? = null,
    val onCallPaymentMethod: OnCallPaymentMethod? = null,
    val onVacationStart: LocalDate? = null,
    val onVacationUntil: LocalDate? = null,
    val version: Int = 0,
) {

    fun isHealthProfessional(): Boolean = type in listOf(
        StaffType.COMMUNITY_SPECIALIST,
        StaffType.HEALTH_PROFESSIONAL,
        StaffType.PARTNER_HEALTH_PROFESSIONAL,
        StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
    )

}

data class CreateUserRequest(
    val staffId: UUID? = RangeUUID.generate(),
    val firstName: String,
    val lastName: String,
    val email: String,
    val nationalId: String? = null,
    val birthdate: LocalDate? = null,
    val gender: Gender,
    val profileImageUrl: String? = null,
    val role: Role,
    val type: StaffType,
    val active: Boolean = true,
    val urlSlug: String? = null,
    val quote: String? = null,
    val profileBio: String? = null,
    val council: CouncilDTO? = null,
    val specialty: UUID? = null,
    val subSpecialties: List<UUID>? = emptyList(),
    val internalSpecialty: UUID? = null,
    val internalSubSpecialties: List<UUID>? = emptyList(),
    val providerUnits: List<UUID>? = emptyList(),
    val qualifications: List<Qualification>? = emptyList(),
    val tier: SpecialistTier? = null,
    val theoristTier: SpecialistTier? = null,
    val curiosity: String? = null,
    val showOnApp: Boolean? = null,
    val education: List<String>? = emptyList(),
    val healthSpecialistScore: HealthSpecialistScoreEnum? = null,
    val deAccreditationDate: LocalDate? = null,
    val contacts: List<ContactModel>? = emptyList(),
    val paymentFrequency: Int? = 0,
    val attendsToOnCall: Boolean? = null,
    val onCallPaymentMethod: OnCallPaymentMethod? = null,
    val onVacationStart: LocalDate? = null,
    val onVacationUntil: LocalDate? = null,
    val version: Int = 0,
)

data class AddressRequest(
    val id: UUID? = null,
    val street: String? = null,
    val number: String? = null,
    val complement: String? = null,
    val neighborhood: String? = null,
    val state: String? = null,
    val city: String? = null,
    val zipcode: String? = null,
    val label: String? = null,
    val active: Boolean = true,
    val latitude: String? = null,
    val longitude: String? = null,
)

data class UserResponse(
    val id: UUID,
    val firstName: String,
    val lastName: String,
    val email: String,
    val nationalId: String? = null,
    val birthdate: LocalDate? = null,
    val gender: Gender,
    val profileImageUrl: String? = null,
    val role: Role? = null,
    val type: StaffType? = null,
    val active: Boolean = true,
    val urlSlug: String? = null,
    val quote: String? = null,
    val profileBio: String? = null,
    val council: CouncilDTO? = null,
    val specialty: UUID? = null,
    val subSpecialties: List<UUID>? = emptyList(),
    val internalSpecialty: UUID? = null,
    val internalSubSpecialties: List<UUID>? = emptyList(),
    val providerUnits: List<UUID>? = emptyList(),
    val qualifications: List<Qualification>? = emptyList(),
    val tier: SpecialistTier? = null,
    val theoristTier: SpecialistTier? = null,
    val curiosity: String? = null,
    val showOnApp: Boolean? = null,
    val education: List<String>? = emptyList(),
    val healthSpecialistScore: HealthSpecialistScoreEnum? = null,
    val deAccreditationDate: LocalDate? = null,
    val contacts: List<ContactModel>? = emptyList(),
    val paymentFrequency: Int? = null,
    val memedStatus: String? = null,
    val attendsToOnCall: Boolean? = null,
    val onCallPaymentMethod: OnCallPaymentMethod? = null,
    val onVacationStart: LocalDate? = null,
    val onVacationUntil: LocalDate? = null,
    val version: Int = 0,
) {
    val fullName: String = "$firstName $lastName"
}

data class CouncilDTO(
    val number: String,
    val state: State,
    val type: Int? = null,
)

data class ContactModel(
    val id: UUID?,
    val address: AddressRequest? = null,
    val phones: List<PhoneNumber> = emptyList(),
    val scheduleAvailabilityDays: Int? = null,
    val modality: ModalityType,
    val availableDays: List<AvailableDay> = emptyList(),
    val website: String? = null,
)
