package br.com.alice.api.ops.services

import br.com.alice.api.ops.controllers.staff.converters.HealthProfessionalConverter
import br.com.alice.api.ops.controllers.staff.converters.StaffConverter
import br.com.alice.api.ops.controllers.staff.converters.UserResponseConverter
import br.com.alice.api.ops.controllers.staff.models.CreateUserRequest
import br.com.alice.api.ops.controllers.staff.models.UserRequest
import br.com.alice.api.ops.controllers.staff.models.UserResponse
import br.com.alice.common.Converter
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.HealthProfessionalOpsProfile
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.models.Staff
import br.com.alice.ehr.client.PrescriptionService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class StaffInternalService(
    val staffService: StaffService,
    private val healthProfessionalService: HealthProfessionalService,
    private val healthProfessionalOpsProfileInternalService: HealthProfessionalOpsProfileInternalService,
    private val prescriptionService: PrescriptionService,
) {
    suspend fun list(filters: Filters?, range: IntRange): Result<List<UserResponse>, Throwable> {
        val staffs = filters?.let {
            staffService.searchByNameAndRoleWithRange(
                namePrefix = filters.namePrefix,
                active = filters.active,
                types = filters.types,
                roles = filters.roles,
                range = range,
                ids = filters.ids
            )
        } ?: staffService.findByRange(range)
        return staffs.mapEach { UserResponseConverter.convert(it) }
    }

    suspend fun count(filters: Filters?): Result<Int, Throwable> =
        filters?.let {
            staffService.countByNameAndRoleWithRange(
                namePrefix = it.namePrefix,
                active = it.active,
                types = it.types,
                roles = it.roles,
                ids = it.ids
            )
        } ?: staffService.count()

    suspend fun get(id: UUID): Result<UserResponse, Throwable> =
        staffService.get(id)
            .map { staff ->
                val memedStatus = getMemedStatus(staff)
                val healthProfessional = getHealthProfessionalIfNecessary(staff)
                val healthProfessionalOpsProfile = if (healthProfessional != null) {
                    healthProfessionalOpsProfileInternalService.getByHealthProfessionalId(healthProfessional.id)
                } else null
                UserResponseConverter.convert(
                    source = staff,
                    healthProfessional = healthProfessional,
                    memedStatus = memedStatus,
                    healthProfessionalOpsProfile = healthProfessionalOpsProfile
                )
            }


    suspend fun update(id: UUID, request: UserRequest): Result<UserResponse, Throwable> {
        val staff = StaffConverter.convert(id, request)
        val hp = buildOrInactiveHealthProfessionalIfNecessary(id, request)
        val hpOpsProfile = buildHealthProfessionalOpsProfileIfNecessary(hp?.id, request)

        return staffService.update(staff, hp, hp?.contacts)
            .flatMap { updatedStaff ->
                val pair = if (hpOpsProfile != null) {
                    healthProfessionalOpsProfileInternalService.upsert(hpOpsProfile).map { Pair(updatedStaff, it) }
                } else {
                    Pair(updatedStaff, null).success()
                }
                pair
            }.map { (updatedStaff, updatedHpOpsProfile) ->
                UserResponseConverter.convert(updatedStaff, hp, healthProfessionalOpsProfile = updatedHpOpsProfile)
            }
    }

    suspend fun createUser(request: CreateUserRequest): Result<UserResponse, Throwable> {
        val userRequest = RequestConverter.convert(request)
        val staff = StaffConverter.convert(request.staffId ?: RangeUUID.generate(), userRequest)
        val hp = buildOrInactiveHealthProfessionalIfNecessary(staff.id, userRequest)
        val hpOpsProfile = buildHealthProfessionalOpsProfileIfNecessary(hp?.id, userRequest)

        return staffService.add(staff, hp, hp?.contacts)
            .flatMap { updatedStaff ->
                val pair = if (hpOpsProfile != null) {
                    healthProfessionalOpsProfileInternalService.upsert(hpOpsProfile).map { Pair(updatedStaff, it) }
                } else {
                    Pair(updatedStaff, null).success()
                }
                pair
            }.map { (updatedStaff, updatedHpOpsProfile) ->
                UserResponseConverter.convert(updatedStaff, hp, healthProfessionalOpsProfile = updatedHpOpsProfile)
            }
    }

    private suspend fun buildOrInactiveHealthProfessionalIfNecessary(
        staffId: UUID,
        request: UserRequest
    ): HealthProfessional? {
        val hp = healthProfessionalService.findByStaffId(
            staffId,
            HealthProfessionalService.FindOptions(withStaff = false, withContact = false)
        ).getOrNullIfNotFound()

        if (hp != null && request.isHealthProfessional().not() && hp.status == SpecialistStatus.ACTIVE) {
            healthProfessionalService.inactivateById(hp.id)
        }

        return if (request.isHealthProfessional()) HealthProfessionalConverter.convert(
            source = request,
            healthProfessionalId = hp?.id ?: staffId,
            staffId = staffId
        ) else null
    }

    private fun buildHealthProfessionalOpsProfileIfNecessary(
        healthProfessionalId: UUID?,
        request: UserRequest
    ): HealthProfessionalOpsProfile? {
        if (healthProfessionalId == null || request.attendsToOnCall == null) return null
        return HealthProfessionalOpsProfile(
            healthProfessionalId = healthProfessionalId,
            attendsToOnCall = request.attendsToOnCall,
            onCallPaymentMethod = request.onCallPaymentMethod
        )
    }

    private suspend fun getHealthProfessionalIfNecessary(staff: Staff): HealthProfessional? =
        if (staff.type.isHealthProfessional()) healthProfessionalService.findByStaffId(
            staff.id,
            HealthProfessionalService.FindOptions(withStaff = false, withContact = true)
        ).getOrNullIfNotFound() else null

    private fun StaffType?.isHealthProfessional(): Boolean =
        this in listOf(
            StaffType.COMMUNITY_SPECIALIST,
            StaffType.HEALTH_PROFESSIONAL,
            StaffType.PARTNER_HEALTH_PROFESSIONAL,
            StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
        )

    private suspend fun getMemedStatus(staff: Staff): String? =
        staff.nationalId?.let {
            if (staff.type.isHealthProfessional()) prescriptionService.getPhysicianStatus(it).getOrNull() else null
        }
}

data class Filters(
    val roles: List<Role>? = null,
    val namePrefix: String? = null,
    val active: Boolean? = null,
    val types: List<StaffType>? = null,
    val ids: List<UUID>? = null
)

object RequestConverter : Converter<CreateUserRequest, UserRequest>(
    CreateUserRequest::class,
    UserRequest::class,
)
