package br.com.alice.channel.core.services.internal.processors

import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.channel.models.Origin
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.communication.firebase.FirebasePush
import br.com.alice.communication.firebase.PushService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.membership.client.DeviceService
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class PushProcessorTest {
    private val deviceService: DeviceService = mockk()
    private val pushService: PushService = mockk()

    private val pushProcessor = PushProcessor(
        deviceService,
        pushService
    )

    private val channelId = "xpto"
    private val staffId = RangeUUID.generate()
    private val personId = PersonId()
    private val channelPersonId = RangeUUID.generate()
    private val channelDocument = ChannelDocument(
        id = channelId,
        channelPersonId = channelPersonId.toString(),
        personId = personId.toString()
    )

    private val messageDocument = MessageDocument(
        id = "abcd",
        aliceId = staffId.toString(),
        userId = staffId.toString(),
        content = "txt",
        type = MessageType.TEXT,
    )

    private val device = TestModelFactory.buildDevice(
        personId = personId,
        deviceId = "device-id"
    )

    private val channelCategory = channelDocument.origin ?: Origin.CHANNELS.description

    private val firebasePushData = mapOf(
        "path_to_navigate" to "channel",
        "parameter" to channelDocument.id!!,
        "properties" to "{\"category\": \"$channelCategory\"}",
        "url" to "https://alice.com.br/app/channel?channel_id=${channelDocument.id()}"

    )

    private val firebasePush = FirebasePush(
        device.deviceId,
        "Mensagem do seu Time",
        "Você recebeu uma mensagem.",
        channelDocument.unreadMessages?.toInt(),
        firebasePushData
    )

    @AfterTest
    fun confirmVerify() = confirmVerified(
        deviceService,
        pushService
    )

    @Test
    fun `#process returns true and send push`() = runBlocking {
        coEvery {
            deviceService.getDeviceByPerson(channelDocument.personId)
        } returns device.success()

        coEvery {
            pushService.send(firebasePush)
        } returns "".success()

        val result = pushProcessor.process(channelDocument, messageDocument)
        assertThat(result).isSuccessWithData(true)

        coVerify(exactly = 1) { deviceService.getDeviceByPerson(any()) }
        coVerify(exactly = 1) { pushService.send(any()) }
    }

    @Test
    fun `#process returns true and DOES NOT send push when channel personId is equal to message userId`() =
        runBlocking {
            val message = messageDocument.copy(userId = channelDocument.channelPersonId)

            val result = pushProcessor.process(channelDocument, message)
            assertThat(result).isSuccessWithData(true)

            coVerify { deviceService wasNot called }
            coVerify { pushService wasNot called }
        }
}
