package br.com.alice.channel.core.services.internal.processors

import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.Origin
import br.com.alice.communication.firebase.FirebasePush
import br.com.alice.communication.firebase.PushService
import br.com.alice.membership.client.DeviceService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch

class PushProcessor(
    private val deviceService: DeviceService,
    private val pushService: PushService
) : PublicMessageProcessor {

    override suspend fun process(channel: ChannelDocument, message: MessageDocument): Result<Boolean, Throwable> =
        span {
            if (channel.channelPersonId == message.userId) true.success()
            else sendPush(channel)
        }

    private suspend fun sendPush(channel: ChannelDocument): Result<<PERSON><PERSON>an, Throwable> = coroutineScope {
        val channelCategory = channel.origin ?: Origin.CHANNELS.description

        val firebasePushData = mapOf(
            "path_to_navigate" to "channel",
            "parameter" to channel.id!!,
            "properties" to "{\"category\": \"$channelCategory\"}",
            "url" to "https://alice.com.br/app/channel?channel_id=${channel.id()}"
        )

        deviceService.getDeviceByPerson(channel.personId).map { device ->
            span("sendPush") {
                launch {
                    pushService.send(
                        FirebasePush(
                            device.deviceId,
                            "Mensagem do seu Time",
                            "Você recebeu uma mensagem.",
                            channel.unreadMessages?.toInt(),
                            firebasePushData
                        )
                    )
                }
                true
            }
        }
    }
}
