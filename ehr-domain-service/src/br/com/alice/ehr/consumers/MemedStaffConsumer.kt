package br.com.alice.ehr.consumers

import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.extensions.notContains
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.common.models.Gender
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.Staff
import br.com.alice.ehr.clients.memed.MemedClient
import br.com.alice.ehr.services.internal.memed.MemedUserService
import br.com.alice.ehr.services.internal.memed.MemedUserServiceImpl
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.event.HealthProfessionalCreatedEvent
import br.com.alice.staff.event.HealthProfessionalUpdatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success

class MemedStaffConsumer(
    memedClient: MemedClient,
    private val healthProfessionalService: HealthProfessionalService,
    private val memedUserService: MemedUserService = MemedUserServiceImpl(memedClient),
) : Consumer() {

    private val baseGenders = listOf(Gender.FEMALE, Gender.MALE)
    private val opsRoles = listOf(Role.NAVIGATOR, Role.CHIEF_NAVIGATOR, Role.CHIEF_NAVIGATOR_OPS, Role.NAVIGATOR_OPS)
    private val acceptedTypes = listOf(
        StaffType.PITAYA,
        StaffType.PARTNER_HEALTH_PROFESSIONAL,
        StaffType.HEALTH_PROFESSIONAL,
        StaffType.COMMUNITY_SPECIALIST,
    )

    suspend fun healthProfessionalCreated(event: HealthProfessionalCreatedEvent): Result<Any, Throwable> =
        span("healthProfessionalCreated") { span ->
            withSubscribersEnvironment {
                span.setAttribute("health_professional_id", event.payload.healthProfessional.id)

                if (event.payload.healthProfessional.staff == null) throw IllegalArgumentException("Health professional without staff")
                event.payload.healthProfessional.let { healthProfessional ->
                    if (healthProfessional.staff!!.isValid()) memedUserService.syncMemedUser(healthProfessional).thenError {
                        logger.error(
                            "Error creating health professional on Memed",
                            "health_professional_id" to healthProfessional.id,
                            it
                        )
                    }
                    else false.success()
                }
            }.recordResult(span)
        }

    suspend fun healthProfessionalUpdated(event: HealthProfessionalUpdatedEvent): Result<Any, Throwable> =
        span("healthProfessionalUpdated") { span ->
            withSubscribersEnvironment {
                span.setAttribute("health_professional_id", event.payload.healthProfessionalId)

                healthProfessionalService.get(
                    event.payload.healthProfessionalId,
                    HealthProfessionalService.FindOptions(withStaff = true)
                ).flatMap { healthProfessional ->
                    if (healthProfessional.staff == null) return@flatMap IllegalArgumentException("Health professional without staff").failure()
                    if (healthProfessional.staff!!.isValid()) memedUserService.syncMemedUser(healthProfessional).thenError {
                        logger.error(
                            "Error updating health professional on Memed",
                            "health_professional_id" to event.payload.healthProfessionalId,
                            it
                        )
                    }
                    else false.success()
                }
            }.recordResult(span)
        }

    private fun Staff.isValid() =
        this.nationalId != null
                && baseGenders.contains(this.gender)
                && this.active
                && opsRoles.notContains(this.role)
                && acceptedTypes.contains(this.type)
}
