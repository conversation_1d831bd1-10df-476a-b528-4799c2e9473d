package br.com.alice.api.backoffice.mappers.staff

import br.com.alice.api.backoffice.transfers.staff.CreateStaffRequest
import br.com.alice.api.backoffice.transfers.staff.UpdateStaffRequest
import br.com.alice.api.backoffice.transfers.staff.ContactDTO
import br.com.alice.api.backoffice.transfers.staff.AddressDTO
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.Contact
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.StructuredAddress

import java.util.UUID

object StaffInputMapper {
    fun toCreate(request: CreateStaffRequest): Staff = Staff(
        id = RangeUUID.generate(),
        firstName = request.firstName,
        lastName = request.lastName,
        email = request.email,
        nationalId = request.nationalId,
        birthdate = request.birthdate,
        gender = request.gender,
        profileImageUrl = request.profileImageUrl,
        role = request.role,
        type = request.type,
        active = request.active
    )

    fun toHealthProfessional(request: CreateStaffRequest, staffId: UUID): HealthProfessional? {
        if (!request.type.isHealthProfessional()) return null

        val council = request.council?.let {
            Council(
                number = it.number,
                state = it.state,
                type = it.type?.let { type ->
                    br.com.alice.common.models.CouncilType.values().find { ct -> ct.code == type }
                }
            )
        } ?: Council(number = "", state = br.com.alice.common.models.State.SP)

        return HealthProfessional(
            id = staffId,
            staffId = staffId,
            profileBio = request.profileBio,
            council = council,
            specialtyId = request.specialty,
            subSpecialtyIds = request.subSpecialties ?: emptyList(),
            internalSpecialtyId = request.internalSpecialty,
            internalSubSpecialtyIds = request.internalSubSpecialties ?: emptyList(),
            quote = request.quote,
            urlSlug = request.urlSlug,
            qualifications = request.qualifications ?: emptyList(),
            imageUrl = request.profileImageUrl,
            education = request.education ?: emptyList(),
            tier = request.tier,
            theoristTier = request.theoristTier,
            providerUnitIds = request.providerUnits ?: emptyList(),
            showOnApp = request.showOnApp ?: true,
            healthSpecialistScore = request.healthSpecialistScore,
            paymentFrequency = request.paymentFrequency ?: 0,
            email = request.email,
            name = "${request.firstName} ${request.lastName}",
            gender = request.gender,
            nationalId = request.nationalId,
            type = request.type,
            role = request.role,
            curiosity = request.curiosity,
            deAccreditationDate = request.deAccreditationDate,
            onVacationStart = request.onVacationStart?.atStartOfDay(),
            onVacationUntil = request.onVacationUntil?.atStartOfDay(),
            contacts = request.contacts?.map { toContact(it) } ?: emptyList()
        )
    }

    private fun toContact(contactDTO: ContactDTO): Contact = Contact(
        id = contactDTO.id ?: RangeUUID.generate(),
        address = contactDTO.address?.let { toStructuredAddress(it) },
        phones = contactDTO.phones,
        scheduleAvailabilityDays = contactDTO.scheduleAvailabilityDays,
        modality = contactDTO.modality,
        availableDays = contactDTO.availableDays,
        website = contactDTO.website
    )

    private fun toStructuredAddress(addressDTO: AddressDTO): StructuredAddress =
        StructuredAddress(
            id = addressDTO.id ?: RangeUUID.generate(),
            street = addressDTO.street ?: "",
            number = addressDTO.number ?: "",
            complement = addressDTO.complement,
            neighborhood = addressDTO.neighborhood ?: "",
            state = addressDTO.state ?: "",
            city = addressDTO.city ?: "",
            zipcode = addressDTO.zipcode ?: "",
            label = addressDTO.label,
            active = addressDTO.active,
            latitude = addressDTO.latitude,
            longitude = addressDTO.longitude
        )

    fun toUpdate(id: UUID, request: UpdateStaffRequest, existingStaff: Staff): Staff = existingStaff.copy(
        firstName = request.firstName,
        lastName = request.lastName,
        email = request.email,
        nationalId = request.nationalId,
        birthdate = request.birthdate,
        gender = request.gender,
        profileImageUrl = request.profileImageUrl,
        role = request.role,
        type = request.type,
        active = request.active
    )

    fun toHealthProfessionalForUpdate(request: UpdateStaffRequest, staffId: UUID, existingHealthProfessionalId: UUID?): HealthProfessional? {
        if (!request.type.isHealthProfessional()) return null

        val council = request.council?.let {
            Council(
                number = it.number,
                state = it.state,
                type = it.type?.let { type ->
                    br.com.alice.common.models.CouncilType.values().find { ct -> ct.code == type }
                }
            )
        } ?: Council(number = "", state = br.com.alice.common.models.State.SP)

        return HealthProfessional(
            id = existingHealthProfessionalId ?: staffId,
            staffId = staffId,
            profileBio = request.profileBio,
            council = council,
            specialtyId = request.specialty,
            subSpecialtyIds = request.subSpecialties ?: emptyList(),
            internalSpecialtyId = request.internalSpecialty,
            internalSubSpecialtyIds = request.internalSubSpecialties ?: emptyList(),
            quote = request.quote,
            urlSlug = request.urlSlug,
            qualifications = request.qualifications ?: emptyList(),
            imageUrl = request.profileImageUrl,
            education = request.education ?: emptyList(),
            tier = request.tier,
            theoristTier = request.theoristTier,
            providerUnitIds = request.providerUnits ?: emptyList(),
            showOnApp = request.showOnApp ?: true,
            healthSpecialistScore = request.healthSpecialistScore,
            paymentFrequency = request.paymentFrequency ?: 0,
            email = request.email,
            name = "${request.firstName} ${request.lastName}",
            gender = request.gender,
            nationalId = request.nationalId,
            type = request.type,
            role = request.role,
            curiosity = request.curiosity,
            deAccreditationDate = request.deAccreditationDate,
            onVacationStart = request.onVacationStart?.atStartOfDay(),
            onVacationUntil = request.onVacationUntil?.atStartOfDay(),
            contacts = request.contacts?.map { toContact(it) } ?: emptyList()
        )
    }

    private fun br.com.alice.common.core.StaffType.isHealthProfessional(): Boolean =
        this in listOf(
            br.com.alice.common.core.StaffType.COMMUNITY_SPECIALIST,
            br.com.alice.common.core.StaffType.HEALTH_PROFESSIONAL,
            br.com.alice.common.core.StaffType.PARTNER_HEALTH_PROFESSIONAL
        )
}
