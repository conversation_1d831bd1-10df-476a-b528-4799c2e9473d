package br.com.alice.common.core.exceptions

import br.com.alice.common.core.BaseConfig
import io.ktor.http.HttpStatusCode
import kotlin.stackTrace

/**
 * This exception cannot be used in the RFC, only inside domain-services or bff-api
 */
open class AliceException(
    message: String,
    val code: String,
    cause: Throwable? = null
) : Exception(message, cause)

abstract class RfcException(
    message: String,
    code: String = "unknown_error",
    cause: Throwable? = null
): AliceException(message, code, cause) {
    abstract val statusCode : HttpStatusCode

    var originDomain: String? = BaseConfig.instance.serviceName
    var throwingCall: String? = this.stackTrace.firstOrNull()?.let { "${it.className}.${it.methodName}" }

    constructor() : this("RFC Error")
}
