package br.com.alice.common.core.extensions

import br.com.alice.common.RangeUUID.DEFAULT_RANGE
import br.com.alice.common.core.PersonId
import br.com.alice.common.range
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class StringExtensionsTest {

    @Test
    fun `#toSnakeCase should convert upper camelcase to snake_case`() {
        assertThat("CamelCase".toSnakeCase()).isEqualTo("camel_case")
    }

    @Test
    fun `#toSnakeCase should convert multiple upper camelcase to snake_case`() {
        assertThat("CamelCaseABC".toSnakeCase()).isEqualTo("camel_case_a_b_c")
    }

    @Test
    fun `#toSnakeCase should convert lower camelcase to snake_case`() {
        assertThat("camelCase".toSnakeCase()).isEqualTo("camel_case")
    }

    @Test
    fun `#toSnakeCase should keep snake_case`() {
        assertThat("snake_case".toSnakeCase()).isEqualTo("snake_case")
    }

    @Test
    fun `#toKebabCase should convert upper camelcase to kebab-case`() {
        assertThat("CamelCase".toKebabCase()).isEqualTo("camel-case")
    }

    @Test
    fun `#toKebabCase should convert lower camelcase to kebab-case`() {
        assertThat("camelCase".toKebabCase()).isEqualTo("camel-case")
    }

    @Test
    fun `#toKebabCase should keep kebab-case`() {
        assertThat("kebab-case".toKebabCase()).isEqualTo("kebab-case")
    }

    @Test
    fun `#snakeToCamelCase should convert snake_case to camelCase`() {
        assertThat("snake_case".snakeToCamelCase()).isEqualTo("snakeCase")
    }

    @Test
    fun `#snakeToCamelCase should keep camelCase`() {
        assertThat("camelCase".snakeToCamelCase()).isEqualTo("camelCase")
    }

    @Test
    fun `#unaccent should unaccent`() {
        assertThat("çÇáéíóúýÁÉÍÓÚÝàèìòùÀÈÌÒÙãõñäëïöüÿÄËÏÖÜÃÕÑâêîôûÂÊÎÔÛ !@#\$%^&*=+'()/_-.,".unaccent())
            .isEqualTo("cCaeiouyAEIOUYaeiouAEIOUaonaeiouyAEIOUAONaeiouAEIOU !@#\$%^&*=+'()/_-.,")
    }

    @Test
    fun `#clearWhitespaces should clear whitespaces`() {
        assertThat(" a b  c   ".clearWhitespaces()).isEqualTo("abc")
    }

    @Test
    fun `#clearWhitespaces should clear whitespaces with any kind of text`() {
        assertThat(
            """ a 
                b  

                c
        """.clearWhitespaces()
        ).isEqualTo("abc")
    }

    @Test
    fun `#replaceTabs should replace tabs`() {
        assertThat("\ta\t\tb c ".replaceTabs()).isEqualTo(" a  b c ")
    }

    @Test
    fun `#onlyAlphanumeric should return alphanumeric string`() {
        assertThat("H32.1".onlyAlphanumeric()).isEqualTo("H321")
        assertThat("H32.132.,321321".onlyAlphanumeric()).isEqualTo("H32132321321")
        assertThat("çÇáéíóúýÁÉÍÓÚÝàèìòùÀÈÌÒÙãõñäëïöüÿÄËÏÖÜÃÕÑâêîôûÂÊÎÔÛ !@#\$%^&*=+'()/_-.,".onlyAlphanumeric())
            .isEqualTo("")
    }

    @Test
    fun `#isValidBrazilianNationalId should return false when string has less than 11 chars`() {
        assertThat("7823".isValidBrazilianNationalId()).isFalse
    }

    @Test
    fun `#isValidBrazilianNationalId should return false when string is an invalid national id`() {
        assertThat("77733322291".isValidBrazilianNationalId()).isFalse
    }

    @Test
    fun `#isValidBrazilianNationalId should return true when string is an valid national id`() {
        assertThat("67295355391".isValidBrazilianNationalId()).isTrue
    }

    @Test
    fun `#toUUID - given a valid UUID as string, should convert it to UUID object successfully`() {
        val uuid = "2cf0f5bc-0e7b-466a-aece-b4d598d12ad2"

        assertThat(uuid.toUUID()).isInstanceOf(UUID::class.java)
    }

    @Test
    fun `#toUUID - given a valid UUID as string, should have the same value`() {
        val uuid = "2cf0f5bc-0e7b-466a-aece-b4d598d12ad2"

        assertThat(uuid.toUUID().toString()).isEqualTo(uuid)
    }

    @Test
    fun `#isUUID - given a valid UUID as string, return true`() {
        val uuid = "2cf0f5bc-0e7b-466a-aece-b4d598d12ad2"

        assertThat(uuid.isUUID()).isTrue
    }

    @Test
    fun `#isUUID - given a invalid UUID as string, should return false`() {
        val fakeUUID = "aaaa"

        assertThat(fakeUUID.isUUID()).isFalse
    }

    @Test
    fun `#isEmail should return true for valid email addresses`() {
        assertThat("<EMAIL>".isEmail()).isTrue()
        assertThat("<EMAIL>".isEmail()).isTrue()
        assertThat("<EMAIL>".isEmail()).isTrue()
        assertThat("<EMAIL>".isEmail()).isTrue()
        assertThat("<EMAIL>".isEmail()).isTrue()
    }

    @Test
    fun `#isEmail should return false for invalid email addresses`() {
        assertThat("<EMAIL>".isEmail()).isFalse()
        assertThat("usuário@inválido".isEmail()).isFalse()
        assertThat("<EMAIL>".isEmail()).isFalse()
        assertThat("<EMAIL>".isEmail()).isFalse()
        assertThat("plainaddress".isEmail()).isFalse()
        assertThat("@missinguser.com".isEmail()).isFalse()
        assertThat("user@.com".isEmail()).isFalse()
        assertThat("email@***************".isEmail()).isFalse()
        assertThat("invalido@".isEmail()).isFalse()
        assertThat("2cf0f5bc-0e7b-466a-aece-b4d598d12ad2".isEmail()).isFalse()
        assertThat("José da Silva".isEmail()).isFalse()
    }

    @Test
    fun `#toCPFMask returns formatted CPF`() {
        assertThat("86948287077".toCPFMask()).isEqualTo("869.482.870-77")
        assertThat("inválido".toCPFMask()).isEqualTo("inválido")
        assertThat("".toCPFMask()).isEqualTo("")
    }

    @Test
    fun `#toPhoneNumberMask returns formatted phone number`() {
        assertThat("11912345678".toPhoneNumberMask()).isEqualTo("(11) 91234-5678")
        assertThat("1112345678".toPhoneNumberMask()).isEqualTo("(11) 1234-5678")
        assertThat("inválido".toPhoneNumberMask()).isEqualTo("inválido")
        assertThat("".toPhoneNumberMask()).isEqualTo("")
    }

    @Test
    fun `#toPhoneNumberMaskWithDDD returns formatted phone number`() {
        assertThat("011912345678".toPhoneNumberMaskWithDDD()).isEqualTo("(011) 91234-5678")
        assertThat("01112345678".toPhoneNumberMaskWithDDD()).isEqualTo("(011) 1234-5678")
        assertThat("inválido".toPhoneNumberMaskWithDDD()).isEqualTo("inválido")
        assertThat("".toPhoneNumberMaskWithDDD()).isEqualTo("")
    }

    @Test
    fun `#toSha256 return SHA-256 hash`() {
        val text = "Hello World"
        val salt = "some salt"
        assertThat(text.toSha256(salt)).isEqualTo("3767a20741e5a870c66640ec4684a748db4e5151c37a6b508ffa2fe2100dc629")
    }

    @Test
    fun `#capitalizeEachWord should capitalize each word of a phrase`() {
        val testName = "john doe da silva"
        assertThat(testName.capitalizeEachWord()).isEqualTo("John Doe Da Silva")
    }

    @Test
    fun `#toUrlEncoded should return encoded string as expected`() {
        val expectedEncodedString = "jos%C3%A9.silva%40alice.com.br"
        val actualEncodedString = "josé<EMAIL>".toUrlEncoded()

        assertThat(actualEncodedString).isEqualTo(expectedEncodedString)
    }

    @Test
    fun `toUUID must return the UUID of the given stringfied UUID`() {
        val stringUUID = "e776adfe-098b-4de1-b075-e29d8d600b38"
        val expectedUUID = "e776adfe-098b-4de1-b075-e29d8d600b38".toUUID()
        assertThat(expectedUUID).isEqualTo(stringUUID.toUUID())
    }

    @Test
    fun `toUUID must thrown an error from any string`() {
        val string = "anything"
        assertFailsWith<IllegalArgumentException> { string.toUUID() }
    }

    @Test
    fun `toSafeUUID must return the UUID of the given stringfied UUID`() {
        val stringUUID = "e776adfe-098b-4de1-b075-e29d8d600b38"
        val expectedUUID = "e776adfe-098b-4de1-b075-e29d8d600b38".toUUID()
        assertThat(expectedUUID).isEqualTo(stringUUID.toSafeUUID())
    }

    @Test
    fun `toSafeUUID must return the UUID generated from any string`() {
        val string = "anything"
        val expectedUUID = "f0e166dc-34d1-3d6c-a28f-fac576c9a43c".toUUID()
        assertThat(expectedUUID).isEqualTo(string.toSafeUUID())
    }

    @Test
    fun `toSafeUUID must return the UUID generated from empty string`() {
        val emptyString = ""
        val anotherEmptyString = "             "
        val expectedUUID = "d41d8cd9-8f00-3204-a980-0998ecf8427e".toUUID()
        assertThat(expectedUUID).isEqualTo(emptyString.toSafeUUID())
        assertThat(expectedUUID).isEqualTo(anotherEmptyString.toSafeUUID())
    }

    @Test
    fun `mask must mask entire string when number of digits to mask is not provided`() {
        val string = "anything".mask()
        val expectedString = "********"
        assertThat(string).isEqualTo(expectedString)
    }

    @Test
    fun `mask must mask some characters of a string when number of digits to mask is provided`() {
        val string = "anything".mask(2)
        val expectedString = "**ything"
        assertThat(string).isEqualTo(expectedString)
    }

    @Test
    fun `toLocalDateTime should return a LocalDateTime base on string`() {
        val string = "2020-12-25T12:13:14"
        val expectedDate = LocalDateTime.of(2020, 12, 25, 12, 13, 14)
        assertThat(string.toLocalDateTime()).isEqualTo(expectedDate)
    }

    @Test
    fun `toLocalDate should return a LocalDate based on the formatter provided or use the default one`() {
        val dateIsoFormat = "2022-01-12"
        val dateBrazilianFormat = "12/05/2021"
        val brazilianFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")

        val expectedDateIsoFormat = LocalDate.of(2022, 1, 12)
        val expectedDateBrazilianFormat = LocalDate.parse(dateBrazilianFormat, brazilianFormatter)

        assertThat(dateIsoFormat.toLocalDate()).isEqualTo(expectedDateIsoFormat)
        assertThat(dateBrazilianFormat.toLocalDate(brazilianFormatter)).isEqualTo(expectedDateBrazilianFormat)
    }

    @Test
    fun `toDotDouble should return double`() {
        val string = "5.040,23"
        val expected = 5040.23
        assertThat(string.toDotDouble()).isEqualTo(expected)
    }

    @Test
    fun `splitIgnoreEmpty must split and filter trailing empty entry`() {
        val string = "1,2,3,"
        val expectedList = listOf("1", "2", "3")

        val actualList = string.splitIgnoreEmpty(",")
        assertEquals(expectedList, actualList)
    }

    @Test
    fun `#onlyNumbers should return only numbers`() {
        assertThat("848.323.848-48".onlyNumbers()).isEqualTo("84832384848")
        assertThat("1234D384@aDJVNAWDJ".onlyNumbers()).isEqualTo("1234384")
    }

    @Test
    fun `#isCnpj should accept numbers and digits in valid format`() {
        assertThat("73727057000160".isCnpj()).isEqualTo(true)
        assertThat("73.727.057/0001-60".isCnpj()).isEqualTo(true)
    }

    @Test
    fun `#isCnpj should return false if cnpj is in invalid format`() {
        assertThat("7372705700096".isCnpj()).isEqualTo(false)
        assertThat("73.727.057/0009-6".isCnpj()).isEqualTo(false)
        assertThat("".isCnpj()).isEqualTo(false)
    }

    @Test
    fun `#toPersonId return expected PersonId`() {
        val personIdStr = "53dec556-d930-45cd-ba7e-ead0d2f83601"
        val expectedPersonId = PersonId(UUID.fromString(personIdStr))

        assertThat(personIdStr.toPersonId()).isEqualTo(expectedPersonId)
    }

    @Test
    fun `toRangeSafeUUID must return the UUID generated from a string uuid in a default range`() {
        val string = "f0e166dc-34d1-3d6c-a28f-fac576c9a43c"
        val expectedUUID = UUID.fromString("f0e166dc-34d1-3d6c-a28f-fac576c9a400")
        val uuid = string.toRangeSafeUUID()
        assertEquals(expectedUUID, uuid)
        assertEquals(DEFAULT_RANGE, uuid.range())
    }

    @Test
    fun `toRangeSafeUUID must return the UUID generated from any string in a default range`() {
        val string = "anything"
        val expectedUUID = UUID.fromString("f0e166dc-34d1-3d6c-a28f-fac576c9a400")
        val uuid = string.toRangeSafeUUID()
        assertEquals(expectedUUID, uuid)
        assertEquals(DEFAULT_RANGE, uuid.range())
    }

    @Test
    fun `isNumeric must return true to numeric values`() {
        val councilNumber = "12345678910"
        assertTrue { councilNumber.isNumeric() }
    }

    @Test
    fun `isNumeric must return false to non numeric values`() {
        assertFalse("nonumeric".isNumeric())
        assertFalse("123nonumeric123".isNumeric())
        assertFalse("123/456-789".isNumeric())
    }

    @Test
    fun `capitalize must capitalize when first char is lower case`() {
        val string = "david lynch"
        val expected = "David lynch"

        val result = string.capitalize()
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `capitalize must return same string when first char is already upper case`() {
        val string = "David lynch"
        val expected = "David lynch"

        val result = string.capitalize()
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#isValidCnpj should accept numbers and digits in valid format`() {
        assertThat("73727057000160".isValidCnpj()).isEqualTo(true)
        assertThat("73.727.057/0001-60".isValidCnpj()).isEqualTo(true)
        assertThat("09552670000195".isValidCnpj()).isEqualTo(true)
        assertThat("60726502000126".isValidCnpj()).isEqualTo(true)
        assertThat("01178071000222".isValidCnpj()).isEqualTo(true)
        assertThat("10.380.188/0001-02".isValidCnpj()).isEqualTo(true)
        assertThat("05.334.617/0002-48".isValidCnpj()).isEqualTo(true)
        assertThat("39.323.654/0003-53".isValidCnpj()).isEqualTo(true)
    }

    @Test
    fun `#isValidCnpj should return false if cnpj is in invalid format`() {
        assertThat("73727057000960".isValidCnpj()).isEqualTo(false)
        assertThat("73.727.057/0009-60".isValidCnpj()).isEqualTo(false)
        assertThat("".isValidCnpj()).isEqualTo(false)
        assertThat("11.111.111/0001-02".isValidCnpj()).isEqualTo(false)
        assertThat("62.463.306/0001-08".isValidCnpj()).isEqualTo(false)
        assertThat("123".isValidCnpj()).isEqualTo(false)
        assertThat("11.111.111/0001-01 ".isValidCnpj()).isEqualTo(false)
        assertThat("111.111.111/0001-01".isValidCnpj()).isEqualTo(false)
        assertThat("11.111.111/0001-012".isValidCnpj()).isEqualTo(false)
        assertThat("aa.bb.ccc/dddd-ee".isValidCnpj()).isEqualTo(false)
        assertThat("01.334.637/0002-48".isValidCnpj()).isEqualTo(false)
    }

    @Test
    fun `#isValidCpf should accept numbers and digits in valid format`() {
        assertThat("073.317.060-98".isValidCpf()).isEqualTo(true)
        assertThat("07331706098".isValidCpf()).isEqualTo(true)
        assertThat("580.178.720-89".isValidCpf()).isEqualTo(true)
        assertThat("61934413011".isValidCpf()).isEqualTo(true)
    }

    @Test
    fun `#isValidCpf should return false if cnpj is in invalid format`() {
        assertThat("1234543332".isValidCpf()).isEqualTo(false)
        assertThat("123".isValidCpf()).isEqualTo(false)
        assertThat("".isValidCpf()).isEqualTo(false)
        assertThat("123.454.333-2".isValidCpf()).isEqualTo(false)
        assertThat("132.222.334-2".isValidCpf()).isEqualTo(false)
        assertThat("9873728372".isValidCpf()).isEqualTo(false)
    }

    @Test
    fun `#withoutCPFMasp should eliminate punctuation`() {
        assertThat("580.178.720-89".withoutCPFMask()).isEqualTo("58017872089")
    }

    @Test
    fun `#normalizeCnpjWithoutMask should normalize a cnpj that is with mask`() {
        val cnpjWithMask = "11.111.111/0001-11"
        val cnpjWithoutMask = cnpjWithMask.normalizeCnpjWithoutMask()
        assertEquals("11111111000111", cnpjWithoutMask)
    }

    @Test
    fun `#isValidZipCode should validate zipCodes correctly`() {
        assertTrue("06020020".isValidZipCode())
        assertTrue("06.020-020".isValidZipCode())
        assertTrue("06020-020".isValidZipCode())
        assertFalse("06020-02".isValidZipCode())
        assertFalse("0..602002".isValidZipCode())
        assertFalse("060".isValidZipCode())
    }

    @Test
    fun `#getFirstAndLastName`() {
        assertThat("José da Silva".getFirstAndLastName()).isEqualTo(Pair("José", "da Silva"))
        assertThat(" José da Silva ".getFirstAndLastName()).isEqualTo(Pair("José", "da Silva"))
        assertThat("José".getFirstAndLastName()).isEqualTo(Pair("José", ""))
        assertThat("".getFirstAndLastName()).isEqualTo(Pair("", ""))
        assertThat("  ".getFirstAndLastName()).isEqualTo(Pair("", ""))
    }

    @Test
    fun testUnescapeJsonWithValidJson() {
        val input =
            "{\"person\": \"{\\\"name\\\": {\\\"first\\\": \\\"John\\\", \\\"last\\\": \\\"Doe\\\"}, \\\"age\\\": 30}\"}"
        val expectedOutput = "{\"person\": {\"name\": {\"first\": \"John\", \"last\": \"Doe\"}, \"age\": 30}}"
        assertEquals(expectedOutput, input.unescapeJson())
    }

    @Test
    fun `#removeOpenAiCitations`() {
        val input = "This is a text with a citation【0:0†any_file_source】."
        val expectedOutput = "This is a text with a citation."
        assertEquals(expectedOutput, input.removeOpenAiCitations())
    }

    @Test
    fun `#removeDoubleQuotes`() {
        val input = "This is a text with \"double quotes\"."
        val expectedOutput = "This is a text with double quotes."
        assertEquals(expectedOutput, input.removeDoubleQuotes())
    }

    @Test
    fun `#notContains should return true when not contains some text into give text`() {
        val input = "This is a message for John."
        assertEquals(input.notContains("jane"), true)
    }

    @Test
    fun `#notContains should return false when contains some text into give text`() {
        val input = "This is a message for John."
        assertEquals(input.notContains("john"), false)
    }

    @Test
    fun `#normalizeWhiteSpaces should remove multiple white spaces`() {
        assertThat("José   Carlos  Fernandez".normalizeWhiteSpaces()).isEqualTo("José Carlos Fernandez")
    }
}
