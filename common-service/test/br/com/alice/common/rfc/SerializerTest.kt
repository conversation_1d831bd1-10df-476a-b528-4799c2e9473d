package br.com.alice.common.rfc

import br.com.alice.common.core.exceptions.AccessForbiddenException
import br.com.alice.common.core.exceptions.AuthorizationException
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.GatewayTimeoutException
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.google.gson.JsonParseException
import io.ktor.client.plugins.HttpRequestTimeoutException
import io.netty.util.internal.PlatformDependent
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Ignore
import kotlin.test.Test
import kotlin.test.assertTrue

class SerializerTest {

    private val stackTraceMethodName = if (PlatformDependent.javaVersion() < 17) "newInstance"
    else "newInstanceWithCaller"

    private val httpRequestTimeoutExceptionJson = """{"error":{"type":"br.com.alice.common.core.exceptions.GatewayTimeoutException","exception":{"message":"Request timeout has expired [url\u003dhttps://data-layer.wonderland.engineering/data/staff/health_professional/findByQuery, request_timeout\u003d300 ms]","code":"gateway_timeout","stackTrace":[{"className":"br.com.alice.common.rfc.SerializerRfcException","methodName":"encode","fileName":"Serializer.kt"},{"className":"br.com.alice.common.rfc.Serializer","methodName":"encodeResult","fileName":"Serializer.kt"},{"className":"br.com.alice.common.rfc.SerializerTest","methodName":"#encodeResult returns a formatted json from exception","fileName":"SerializerTest.kt"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke0","fileName":"NativeMethodAccessorImpl.java"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke","fileName":"NativeMethodAccessorImpl.java"}]}}}"""
    private val notFoundExceptionJson = """{"error":{"type":"br.com.alice.common.core.exceptions.NotFoundException","exception":{"message":"not found","code":"resource_not_found","stackTrace":[{"className":"br.com.alice.common.rfc.SerializerTest","methodName":"\u003cinit\u003e","fileName":"SerializerTest.kt"},{"className":"jdk.internal.reflect.NativeConstructorAccessorImpl","methodName":"newInstance0","fileName":"NativeConstructorAccessorImpl.java"},{"className":"jdk.internal.reflect.NativeConstructorAccessorImpl","methodName":"newInstance","fileName":"NativeConstructorAccessorImpl.java"},{"className":"jdk.internal.reflect.DelegatingConstructorAccessorImpl","methodName":"newInstance","fileName":"DelegatingConstructorAccessorImpl.java"},{"className":"java.lang.reflect.Constructor","methodName":"$stackTraceMethodName","fileName":"Constructor.java"}]}}}"""
    private val authorizationExceptionJson = """{"error":{"type":"br.com.alice.common.core.exceptions.AuthorizationException","exception":{"message":"authorization","code":"authorization","stackTrace":[{"className":"br.com.alice.common.rfc.SerializerTest","methodName":"\u003cinit\u003e","fileName":"SerializerTest.kt"},{"className":"jdk.internal.reflect.NativeConstructorAccessorImpl","methodName":"newInstance0","fileName":"NativeConstructorAccessorImpl.java"},{"className":"jdk.internal.reflect.NativeConstructorAccessorImpl","methodName":"newInstance","fileName":"NativeConstructorAccessorImpl.java"},{"className":"jdk.internal.reflect.DelegatingConstructorAccessorImpl","methodName":"newInstance","fileName":"DelegatingConstructorAccessorImpl.java"},{"className":"java.lang.reflect.Constructor","methodName":"$stackTraceMethodName","fileName":"Constructor.java"}]}}}"""
    private val accessForbiddenExceptionJson = """{"error":{"type":"br.com.alice.common.core.exceptions.AccessForbiddenException","exception":{"message":"access forbidden","code":"access_forbidden","stackTrace":[{"className":"br.com.alice.common.rfc.SerializerTest","methodName":"\u003cinit\u003e","fileName":"SerializerTest.kt"},{"className":"jdk.internal.reflect.NativeConstructorAccessorImpl","methodName":"newInstance0","fileName":"NativeConstructorAccessorImpl.java"},{"className":"jdk.internal.reflect.NativeConstructorAccessorImpl","methodName":"newInstance","fileName":"NativeConstructorAccessorImpl.java"},{"className":"jdk.internal.reflect.DelegatingConstructorAccessorImpl","methodName":"newInstance","fileName":"DelegatingConstructorAccessorImpl.java"},{"className":"java.lang.reflect.Constructor","methodName":"$stackTraceMethodName","fileName":"Constructor.java"}]}}}"""
    private val invalidArgumentExceptionJson = """{"error":{"type":"br.com.alice.common.core.exceptions.InvalidArgumentException","exception":{"message":"invalid argument","code":"invalid_argument","stackTrace":[{"className":"br.com.alice.common.rfc.SerializerTest","methodName":"\u003cinit\u003e","fileName":"SerializerTest.kt"},{"className":"jdk.internal.reflect.NativeConstructorAccessorImpl","methodName":"newInstance0","fileName":"NativeConstructorAccessorImpl.java"},{"className":"jdk.internal.reflect.NativeConstructorAccessorImpl","methodName":"newInstance","fileName":"NativeConstructorAccessorImpl.java"},{"className":"jdk.internal.reflect.DelegatingConstructorAccessorImpl","methodName":"newInstance","fileName":"DelegatingConstructorAccessorImpl.java"},{"className":"java.lang.reflect.Constructor","methodName":"$stackTraceMethodName","fileName":"Constructor.java"}]}}}"""
    private val jsonParseExceptionJson = """{"error":{"type":"br.com.alice.common.core.exceptions.InvalidArgumentException","exception":{"message":"json parse","code":"invalid_argument","stackTrace":[{"className":"br.com.alice.common.rfc.SerializerRfcException","methodName":"encode","fileName":"Serializer.kt"},{"className":"br.com.alice.common.rfc.Serializer","methodName":"encodeResult","fileName":"Serializer.kt"},{"className":"br.com.alice.common.rfc.SerializerTest","methodName":"#encodeResult returns a formatted json from exception","fileName":"SerializerTest.kt"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke0","fileName":"NativeMethodAccessorImpl.java"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke","fileName":"NativeMethodAccessorImpl.java"}]}}}"""
    private val illegalArgumentExceptionJson = """{"error":{"type":"br.com.alice.common.core.exceptions.InvalidArgumentException","exception":{"message":"illegal argument","code":"invalid_argument","stackTrace":[{"className":"br.com.alice.common.rfc.SerializerRfcException","methodName":"encode","fileName":"Serializer.kt"},{"className":"br.com.alice.common.rfc.Serializer","methodName":"encodeResult","fileName":"Serializer.kt"},{"className":"br.com.alice.common.rfc.SerializerTest","methodName":"#encodeResult returns a formatted json from exception","fileName":"SerializerTest.kt"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke0","fileName":"NativeMethodAccessorImpl.java"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke","fileName":"NativeMethodAccessorImpl.java"}]}}}"""
    private val exceptionJson = """{"error":{"type":"br.com.alice.common.core.exceptions.InternalServiceErrorException","exception":{"message":"message","code":"internal_server_error","stackTrace":[{"className":"br.com.alice.common.rfc.SerializerRfcException","methodName":"encode","fileName":"Serializer.kt"},{"className":"br.com.alice.common.rfc.Serializer","methodName":"encodeResult","fileName":"Serializer.kt"},{"className":"br.com.alice.common.rfc.SerializerTest","methodName":"#encodeResult returns a formatted json from exception","fileName":"SerializerTest.kt"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke0","fileName":"NativeMethodAccessorImpl.java"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke","fileName":"NativeMethodAccessorImpl.java"}]}}}"""
    private val duplicatedItemExceptionJson = """{"error":{"type":"br.com.alice.common.core.exceptions.DuplicatedItemException","exception":{"message":"Duplicated item","code":"duplicated_item","stackTrace":[{"className":"br.com.alice.common.rfc.SerializerTest","methodName":"\u003cinit\u003e","fileName":"SerializerTest.kt"},{"className":"jdk.internal.reflect.NativeConstructorAccessorImpl","methodName":"newInstance0","fileName":"NativeConstructorAccessorImpl.java"},{"className":"jdk.internal.reflect.NativeConstructorAccessorImpl","methodName":"newInstance","fileName":"NativeConstructorAccessorImpl.java"},{"className":"jdk.internal.reflect.DelegatingConstructorAccessorImpl","methodName":"newInstance","fileName":"DelegatingConstructorAccessorImpl.java"},{"className":"java.lang.reflect.Constructor","methodName":"$stackTraceMethodName","fileName":"Constructor.java"}]}}}"""

    private val httpRequestTimeoutException = HttpRequestTimeoutException("https://data-layer.wonderland.engineering/data/staff/health_professional/findByQuery", 300)
    private val notFoundException = NotFoundException("not found")
    private val authorizationException = AuthorizationException("authorization")
    private val accessForbiddenException = AccessForbiddenException("access forbidden")
    private val invalidArgumentException = InvalidArgumentException(message = "invalid argument")
    private val jsonParseException = JsonParseException("json parse")
    private val illegalArgumentException = IllegalArgumentException("illegal argument", Exception("cause"))
    private val exception = Exception("message", Exception("cause"))
    private val duplicatedItemException = DuplicatedItemException()

    @Test
    fun `#decodeResult returns result from json error`() {
        val resultHttpRequestTimeoutException = Serializer.decodeResult<String>(httpRequestTimeoutExceptionJson)
        val resultNotFoundException = Serializer.decodeResult<String>(notFoundExceptionJson)
        val resultAuthorizationException = Serializer.decodeResult<String>(authorizationExceptionJson)
        val resultAccessForbiddenException = Serializer.decodeResult<String>(accessForbiddenExceptionJson)
        val resultInvalidArgumentException = Serializer.decodeResult<String>(invalidArgumentExceptionJson)
        val resultJsonParseException = Serializer.decodeResult<String>(jsonParseExceptionJson)
        val resultIllegalArgumentException = Serializer.decodeResult<String>(illegalArgumentExceptionJson)
        val resultException = Serializer.decodeResult<String>(exceptionJson)
        val resultDuplicatedItemException = Serializer.decodeResult<String>(duplicatedItemExceptionJson)
        val notFoundException = Serializer.decodeResult<String>(notFoundExceptionJson)

        assertThat(resultHttpRequestTimeoutException).fails().withMessage("Request timeout has expired [url=https://data-layer.wonderland.engineering/data/staff/health_professional/findByQuery, request_timeout=300 ms]").ofType(GatewayTimeoutException::class)
        assertThat(resultNotFoundException).fails().withMessage("not found").ofType(NotFoundException::class)
        assertThat(resultAuthorizationException).fails().withMessage("authorization").ofType(AuthorizationException::class)
        assertThat(resultAccessForbiddenException).fails().withMessage("access forbidden").ofType(AccessForbiddenException::class)
        assertThat(resultInvalidArgumentException).fails().withMessage("invalid argument").ofType(InvalidArgumentException::class)
        assertThat(resultJsonParseException).fails().withMessage("json parse").ofType(InvalidArgumentException::class)
        assertThat(resultIllegalArgumentException).fails().withMessage("illegal argument").ofType(InvalidArgumentException::class)
        assertThat(resultException).fails().withMessage("message").ofType(InternalServiceErrorException::class)
        assertThat(resultDuplicatedItemException).fails().withMessage("Duplicated item").ofType(DuplicatedItemException::class)
        assertThat(notFoundException).fails().withMessage("not found").ofType(NotFoundException::class)
    }

    @Ignore
    @Test
    fun `#encodeResult returns a formatted json from exception`() {
        val httpRequestTimeoutException = Serializer.encodeResult(httpRequestTimeoutException.failure()).removeLineNumber()
        val notFoundException = Serializer.encodeResult(notFoundException.failure()).removeLineNumber()
        val authorizationException = Serializer.encodeResult(authorizationException.failure()).removeLineNumber()
        val accessForbiddenException = Serializer.encodeResult(accessForbiddenException.failure()).removeLineNumber()
        val invalidArgumentException = Serializer.encodeResult(invalidArgumentException.failure()).removeLineNumber()
        val jsonParseException = Serializer.encodeResult(jsonParseException.failure()).removeLineNumber()
        val illegalArgumentException = Serializer.encodeResult(illegalArgumentException.failure()).removeLineNumber()
        val exception = Serializer.encodeResult(exception.failure()).removeLineNumber()
        val duplicatedItemException = Serializer.encodeResult(duplicatedItemException.failure()).removeLineNumber()

        assertThat(httpRequestTimeoutException).isEqualTo(httpRequestTimeoutExceptionJson.removeLineNumber())
        assertThat(notFoundException).isEqualTo(notFoundExceptionJson.removeLineNumber())
        assertThat(authorizationException).isEqualTo(authorizationExceptionJson.removeLineNumber())
        assertThat(accessForbiddenException).isEqualTo(accessForbiddenExceptionJson.removeLineNumber())
        assertThat(invalidArgumentException).isEqualTo(invalidArgumentExceptionJson.removeLineNumber())
        assertThat(jsonParseException).isEqualTo(jsonParseExceptionJson.removeLineNumber())
        assertThat(illegalArgumentException).isEqualTo(illegalArgumentExceptionJson.removeLineNumber())
        assertThat(exception).isEqualTo(exceptionJson.removeLineNumber())
        assertThat(duplicatedItemException).isEqualTo(duplicatedItemExceptionJson.removeLineNumber())
    }

    @Test
    fun `#encode and decode exception`() {
        val notFound = NotFoundException(
            message = "notFound",
            code = "resource_not_found",
            cause = jsonParseException,
        )

        val stackTrace = listOf(
            StackTraceElement(
                "className_1",
                "methodName_1",
                "fileName_1",
                10
            ),
            StackTraceElement(
                "className_2",
                "methodName_2",
                "fileName_2",
                20
            )
        ).toTypedArray()

        notFound.stackTrace = stackTrace

        val json = SerializerRfcException.encode(notFound)

        val finalJson = Serializer.encode(mapOf("error" to json))

        assertThat(finalJson.removeLineNumber()).isEqualTo("""{"error":{"type":"br.com.alice.common.core.exceptions.NotFoundException","exception":{"message":"notFound","code":"resource_not_found","originDomain":"UNKNOWN","throwingCall":"br.com.alice.common.rfc.SerializerTest.#encode and decode exception","stackTrace":[{"className":"className_1","methodName":"methodName_1","fileName":"fileName_1"},{"className":"className_2","methodName":"methodName_2","fileName":"fileName_2"}]}}}""")

        val result = Serializer.decodeResult<String>(finalJson)
        assertTrue(result is Result.Failure)

        val exception = result.error as NotFoundException

        assertThat(exception.message).isEqualTo("notFound")
        assertThat(exception.code).isEqualTo("resource_not_found")
        assertThat(exception.originDomain).isEqualTo("UNKNOWN")
        assertThat(exception.throwingCall).isEqualTo("br.com.alice.common.rfc.SerializerTest.#encode and decode exception")
//        commented until migrate
//        assertThat(exception.cause).isInstanceOf(Exception::class.java)
//        assertThat(exception.cause!!.message).isEqualTo("com.google.gson.JsonParseException : json parse")
        assertThat(exception.stackTrace).isEqualTo(stackTrace)
    }

    @Test
    fun `#encode and decode new exception pattern HttpRequestTimeoutException to GatewayTimeoutException`() {
        val json = SerializerRfcException.encode(httpRequestTimeoutException)

        val finalJson = Serializer.encode(mapOf("error" to json))

        assertThat(finalJson.removeLineNumber()).isEqualTo("""{"error":{"type":"br.com.alice.common.core.exceptions.GatewayTimeoutException","exception":{"message":"Request timeout has expired [url\u003dhttps://datalayer.wonderland.engineering/data/staff/health_professional/findByQuery, request_timeout\u003d300 ms]","code":"gateway_timeout","originDomain":"UNKNOWN","throwingCall":"br.com.alice.common.rfc.SerializerRfcException.encode","stackTrace":[{"className":"br.com.alice.common.rfc.SerializerRfcException","methodName":"encode","fileName":"Serializer.kt"},{"className":"br.com.alice.common.rfc.SerializerTest","methodName":"#encode and decode new exception pattern HttpRequestTimeoutException to GatewayTimeoutException","fileName":"SerializerTest.kt"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke0","fileName":"NativeMethodAccessorImpl.java"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke","fileName":"NativeMethodAccessorImpl.java"},{"className":"jdk.internal.reflect.DelegatingMethodAccessorImpl","methodName":"invoke","fileName":"DelegatingMethodAccessorImpl.java"}]}}}""".removeLineNumber())

        val result = Serializer.decodeResult<String>(finalJson)
        assertTrue(result is Result.Failure)

        val exception = result.error as GatewayTimeoutException

        assertThat(exception.message).isEqualTo("Request timeout has expired [url=https://data-layer.wonderland.engineering/data/staff/health_professional/findByQuery, request_timeout=300 ms]")
        assertThat(exception.code).isEqualTo("gateway_timeout")
        assertThat(exception.originDomain).isEqualTo("UNKNOWN")
        assertThat(exception.throwingCall).isEqualTo("br.com.alice.common.rfc.SerializerRfcException.encode")
    }

    @Test
    fun `#encode and decode new exception pattern JsonParseException to InvalidArgumentException`() {
        val json = SerializerRfcException.encode(jsonParseException)

        val finalJson = Serializer.encode(mapOf("error" to json))

        assertThat(finalJson.removeLineNumber()).isEqualTo("""{"error":{"type":"br.com.alice.common.core.exceptions.InvalidArgumentException","exception":{"message":"json parse","code":"invalid_argument","originDomain":"UNKNOWN","throwingCall":"br.com.alice.common.rfc.SerializerRfcException.encode","stackTrace":[{"className":"br.com.alice.common.rfc.SerializerRfcException","methodName":"encode","fileName":"Serializer.kt"},{"className":"br.com.alice.common.rfc.SerializerTest","methodName":"#encode and decode new exception pattern JsonParseException to InvalidArgumentException","fileName":"SerializerTest.kt"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke0","fileName":"NativeMethodAccessorImpl.java"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke","fileName":"NativeMethodAccessorImpl.java"},{"className":"jdk.internal.reflect.DelegatingMethodAccessorImpl","methodName":"invoke","fileName":"DelegatingMethodAccessorImpl.java"}]}}}""".removeLineNumber())

        val result = Serializer.decodeResult<String>(finalJson)
        assertTrue(result is Result.Failure)

        val exception = result.error as InvalidArgumentException

        assertThat(exception.message).isEqualTo("json parse")
        assertThat(exception.code).isEqualTo("invalid_argument")
        assertThat(exception.originDomain).isEqualTo("UNKNOWN")
        assertThat(exception.throwingCall).isEqualTo("br.com.alice.common.rfc.SerializerRfcException.encode")
        assertThat(exception.stackTrace.size).isEqualTo(5)
    }

    @Test
    fun `#encode and decode new exception pattern IllegalArgumentException to InvalidArgumentException`() {
        val json = SerializerRfcException.encode(illegalArgumentException)

        val finalJson = Serializer.encode(mapOf("error" to json))

        assertThat(finalJson.removeLineNumber()).isEqualTo("""{"error":{"type":"br.com.alice.common.core.exceptions.InvalidArgumentException","exception":{"message":"illegal argument","code":"invalid_argument","originDomain":"UNKNOWN","throwingCall":"br.com.alice.common.rfc.SerializerRfcException.encode","stackTrace":[{"className":"br.com.alice.common.rfc.SerializerRfcException","methodName":"encode","fileName":"Serializer.kt"},{"className":"br.com.alice.common.rfc.SerializerTest","methodName":"#encode and decode new exception pattern IllegalArgumentException to InvalidArgumentException","fileName":"SerializerTest.kt"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke0","fileName":"NativeMethodAccessorImpl.java"},{"className":"jdk.internal.reflect.NativeMethodAccessorImpl","methodName":"invoke","fileName":"NativeMethodAccessorImpl.java"},{"className":"jdk.internal.reflect.DelegatingMethodAccessorImpl","methodName":"invoke","fileName":"DelegatingMethodAccessorImpl.java"}]}}}""".removeLineNumber())

        val result = Serializer.decodeResult<String>(finalJson)
        assertTrue(result is Result.Failure)

        val exception = result.error as InvalidArgumentException

        assertThat(exception.message).isEqualTo("illegal argument")
        assertThat(exception.code).isEqualTo("invalid_argument")
        assertThat(exception.originDomain).isEqualTo("UNKNOWN")
        assertThat(exception.throwingCall).isEqualTo("br.com.alice.common.rfc.SerializerRfcException.encode")
        assertThat(exception.stackTrace.size).isEqualTo(5)
    }


    private fun String.removeLineNumber() = this.replace(",\"lineNumber\":(\\d*)|(-\\d*)".toRegex(), "")
}
