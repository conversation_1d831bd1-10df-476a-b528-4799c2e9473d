package br.com.alice.sales_channel.controller

import br.com.alice.common.Response
import br.com.alice.common.core.extensions.onlyAlphanumeric
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.SALES_CHANNEL_DOMAIN_SERVICE_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.DealChannel
import br.com.alice.data.layer.models.DealStage
import br.com.alice.data.layer.models.OngoingCompanyDeal
import br.com.alice.data.layer.models.OngoingCompanyDealDetails
import br.com.alice.data.layer.models.SalesFirmAgentPartnership
import br.com.alice.sales_channel.model.DealRequest
import br.com.alice.sales_channel.model.DealStatusRequest
import br.com.alice.sales_channel.service.OngoingCompanyDealService
import br.com.alice.sales_channel.service.SalesFirmAgentPartnershipService
import br.com.alice.sales_channel.service.SalesFirmService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.success
import io.ktor.http.HttpStatusCode
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger

class BackfillDealController(
    private val dealService: OngoingCompanyDealService,
    private val salesFirmService: SalesFirmService,
    private val salesFirmAgentPartnershipService: SalesFirmAgentPartnershipService,
) : BackfillBaseController() {
    suspend fun addDeal(request: DealRequest): Response =
        withUnauthenticatedTokenWithKey(SALES_CHANNEL_DOMAIN_SERVICE_ROOT_SERVICE_NAME) {
            val sellerName = getNonNullable(request.sellerName, "name")
            val salesFirm = salesFirmService.getSalesFirmByName(sellerName).getOrNull()
                ?: return@withUnauthenticatedTokenWithKey false.success()

            val partnershipId = getOrCreatePartnership(request.salesAgentId, salesFirm.id)

            logger.info(
                "Creating deal",
                "name" to request.companyName,
                "seller_name" to request.sellerName
            )

            dealService.add(
                OngoingCompanyDeal(
                    name = getNonNullable(request.companyName, "name"),
                    salesFirmId = salesFirm.id,
                    salesAgentDocument = request.sellerDocument,
                    cnpj = getNonNullable(request.companyDocument, "cnpj"),
                    status = DealStage.valueOf(request.status),
                    legalName = request.companyLegalName,
                    dealDetails = OngoingCompanyDealDetails(
                        employeeCount = request.employeeCount,
                        livesCount = request.livesCount,
                        contractModel = request.contractModel
                    ),
                    sourceCreatedAt = toDateTime(request.createdAt, "createdAt"),
                    sourceUpdatedAt = toDateTime(request.updatedAt, "updatedAt"),
                    sourceId = request.id,
                    channel = DealChannel.BROKER,
                    companyId = request.companyId,
                    salesFirmAgentPartnershipId = partnershipId,
                    salesAgentId = request.salesAgentId,
                )
            )
        }.foldResponse()

    private suspend fun getOrCreatePartnership(salesAgentId: UUID?, salesFirmId: UUID?): UUID? {
        if (salesFirmId == null || salesAgentId == null) {
            return null
        }

        val partnership = salesFirmAgentPartnershipService.getBySalesFirmAndAgent(
            salesFirmId = salesFirmId!!,
            salesAgentId = salesAgentId!!,
        ).getOrNull()

        return partnership?.id ?: salesFirmAgentPartnershipService.create(
            SalesFirmAgentPartnership(
                salesFirmId = salesFirmId,
                salesAgentId = salesAgentId,
            )
        ).get().id
    }

    suspend fun move(request: DealStatusRequest): Response =
        withUnauthenticatedTokenWithKey(SALES_CHANNEL_DOMAIN_SERVICE_ROOT_SERVICE_NAME) {
            dealService.getBySourceId(request.id).get().let { deal ->
                dealService.update(
                    ongoingCompanyDeal = deal.copy(status = DealStage.valueOf(request.status)),
                    shouldValidateStatus = request.shouldValidateStatus,
                )
            }
        }.foldResponse()

    suspend fun populateSearchTokens(request: BackfillRequest): Response =
        withBackfillEnvironment {
            val ongoingCompanyDeals = dealService.getByRange(request.offset, request.limit).get()

            logger.info("BackfillDealController#populateSearchTokens", "deals" to ongoingCompanyDeals.size)

            backfill(ongoingCompanyDeals) { deal ->
                val salesAgentDocument = deal.salesAgentDocument?.onlyAlphanumeric() ?: ""
                val cnpjNumbers = deal.cnpj.onlyAlphanumeric()
                val updated = deal.copy(
                    searchTokens = "${deal.name} ${deal.cnpj} $cnpjNumbers $salesAgentDocument".unaccent()
                )

                dealService
                    .upsertOngoingCompanyDeal(updated)
                    .fold({
                        logger.info(
                            "BackfillDealController: OngoingCompanyDeal updated with success",
                            "deal" to it
                        )
                        it.success()
                    }, {
                        logger.error(
                            "BackfillDealController: Error to update clinical outcome",
                            "deal" to deal,
                            "error" to it
                        )
                        it.failure()
                    })
            }
        }

    suspend fun delete(id: String): Response =
        withBackfillEnvironment {
            dealService.delete(id.toUUID()).foldResponse()
        }

    suspend fun update(request: BackfillDealRequest): Response =
        withBackfillEnvironment {
            dealService.get(request.id.toUUID()).get().let { deal ->
                val updated = deal.copy(
                    companyId = request.companyId,
                    name = getNonNullable(request.companyName, "companyName"),
                    legalName = request.companyLegalName,
                    cnpj = getNonNullable(request.companyDocument, "companyDocument"),
                    salesAgentDocument = request.sellerDocument,
                    status = DealStage.valueOf(request.status),
                    salesAgentId = request.salesAgentId,
                    channel = request.channel,
                    salesFirmAgentPartnershipId = getOrCreatePartnership(request.salesAgentId, deal.salesFirmId)
                )
                dealService.updateById(request.id.toUUID(), updated).foldResponse()
            }
        }

    private suspend fun <M> backfill(
        list: List<M>,
        func: suspend (M) -> Result<OngoingCompanyDeal, Throwable>
    ): Response {
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)

        list.pmap { func.invoke(it).fold({ successCount.getAndIncrement() }, { errorsCount.getAndIncrement() }) }

        return Response(HttpStatusCode.OK, BackfillResponse(successCount.get(), errorsCount.get()))
    }
}

fun getNonNullable(value: String?, field: String): String {
    if (value.isNullOrBlank()) {
        throw IllegalArgumentException(" $field is null or blank")
    }
    return value;
}

fun toDateTime(value: String?, field: String): LocalDateTime {
    if (value.isNullOrBlank()) {
        throw IllegalArgumentException(" $field is null or blank")
    }

    val instant = Instant.ofEpochMilli(value.toLong())
    return LocalDateTime.ofInstant(instant, ZoneId.systemDefault())
}


data class BackfillRequest(val offset: Int, val limit: Int)
data class BackfillResponse(val successCount: Int, val errorsCount: Int)
data class BackfillDealRequest(
    val id: String,
    val companyId: UUID?,
    val companyName: String,
    val companyLegalName: String?,
    val companyDocument: String,
    val sellerName: String,
    val sellerDocument: String?,
    val status: String,
    val salesAgentId: UUID?,
    val channel: DealChannel)
